// Copyright Epic Games, Inc. All Rights Reserved.

//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.1
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public class FDatasmithFacadeMaterialInstance : FDatasmithFacadeBaseMaterial {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;

  internal FDatasmithFacadeMaterialInstance(global::System.IntPtr cPtr, bool cMemoryOwn) : base(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_SWIGUpcast(cPtr), cMemoryOwn) {
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeMaterialInstance obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  protected override void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeMaterialInstance(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
      base.Dispose(disposing);
    }
  }

  public FDatasmithFacadeMaterialInstance(string InElementName) : this(DatasmithFacadeCSharpPINVOKE.new_FDatasmithFacadeMaterialInstance(InElementName), true) {
  }

  public FDatasmithFacadeMaterialInstance.EMaterialInstanceType GetMaterialType() {
    FDatasmithFacadeMaterialInstance.EMaterialInstanceType ret = (FDatasmithFacadeMaterialInstance.EMaterialInstanceType)DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_GetMaterialType(swigCPtr);
    return ret;
  }

  public void SetMaterialType(FDatasmithFacadeMaterialInstance.EMaterialInstanceType InMaterialInstanceType) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_SetMaterialType(swigCPtr, (int)InMaterialInstanceType);
  }

  public FDatasmithFacadeMaterialInstance.EMaterialInstanceQuality GetQuality() {
    FDatasmithFacadeMaterialInstance.EMaterialInstanceQuality ret = (FDatasmithFacadeMaterialInstance.EMaterialInstanceQuality)DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_GetQuality(swigCPtr);
    return ret;
  }

  public void SetQuality(FDatasmithFacadeMaterialInstance.EMaterialInstanceQuality InQuality) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_SetQuality(swigCPtr, (int)InQuality);
  }

  public string GetCustomMaterialPathName() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_GetCustomMaterialPathName(swigCPtr));
    return ret;
  }

  public void SetCustomMaterialPathName(string InPathName) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_SetCustomMaterialPathName(swigCPtr, InPathName);
  }

  public void AddColor(string InPropertyName, byte InR, byte InG, byte InB, byte InA) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_AddColor__SWIG_0(swigCPtr, InPropertyName, InR, InG, InB, InA);
  }

  public void AddColor(string InPropertyName, float InR, float InG, float InB, float InA) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_AddColor__SWIG_1(swigCPtr, InPropertyName, InR, InG, InB, InA);
  }

  public void AddTexture(string InPropertyName, FDatasmithFacadeTexture InTexture) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_AddTexture(swigCPtr, InPropertyName, FDatasmithFacadeTexture.getCPtr(InTexture));
  }

  public void AddString(string InPropertyName, string InPropertyValue) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_AddString(swigCPtr, InPropertyName, InPropertyValue);
  }

  public void AddFloat(string InPropertyName, float InPropertyValue) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_AddFloat(swigCPtr, InPropertyName, InPropertyValue);
  }

  public void AddBoolean(string InPropertyName, bool bInPropertyValue) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_AddBoolean(swigCPtr, InPropertyName, bInPropertyValue);
  }

  public int GetPropertiesCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_GetPropertiesCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeKeyValueProperty GetProperty(int PropertyIndex) {
	global::System.IntPtr objectPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_GetProperty(swigCPtr, PropertyIndex);
	if(objectPtr == global::System.IntPtr.Zero)
	{
		return null;
	}
	else
	{
		return new FDatasmithFacadeKeyValueProperty(objectPtr, true);
	}
}

  public FDatasmithFacadeKeyValueProperty GetPropertyByName(string PropertyName) {
	global::System.IntPtr objectPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialInstance_GetPropertyByName(swigCPtr, PropertyName);
	if(objectPtr == global::System.IntPtr.Zero)
	{
		return null;
	}
	else
	{
		return new FDatasmithFacadeKeyValueProperty(objectPtr, true);
	}
}

  public enum EMaterialInstanceType {
    Auto,
    Opaque,
    Transparent,
    ClearCoat,
    Custom,
    CutOut,
    Emissive,
    Decal,
    Count
  }

  public enum EMaterialInstanceQuality {
    High,
    Low,
    Count
  }

}
