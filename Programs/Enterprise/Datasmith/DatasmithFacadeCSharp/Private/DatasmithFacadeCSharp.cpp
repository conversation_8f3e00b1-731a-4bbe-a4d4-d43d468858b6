// Copyright Epic Games, Inc. All Rights Reserved.

/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * This file is not intended to be easily readable and contains a number of
 * coding conventions designed to improve portability and efficiency. Do not make
 * changes to this file unless you know what you are doing--modify the SWIG
 * interface file instead.
 * ----------------------------------------------------------------------------- */

// Copyright Epic Games, Inc. All Rights Reserved.

#include "DatasmithFacadeCSharp.h"



#ifndef SWIGCSHARP
#define SWIGCSHARP
#endif



#ifdef __cplusplus
/* SwigValueWrapper is described in swig.swg */
template<typename T> class SwigValueWrapper {
  struct SwigMovePointer {
    T *ptr;
    SwigMovePointer(T *p) : ptr(p) { }
    ~SwigMovePointer() { delete ptr; }
    SwigMovePointer& operator=(SwigMovePointer& rhs) { T* oldptr = ptr; ptr = 0; delete oldptr; ptr = rhs.ptr; rhs.ptr = 0; return *this; }
  } pointer;
  SwigValueWrapper& operator=(const SwigValueWrapper<T>& rhs);
  SwigValueWrapper(const SwigValueWrapper<T>& rhs);
public:
  SwigValueWrapper() : pointer(0) { }
  SwigValueWrapper& operator=(const T& t) { SwigMovePointer tmp(new T(t)); pointer = tmp; return *this; }
  operator T&() const { return *pointer.ptr; }
  T *operator&() { return pointer.ptr; }
};

template <typename T> T SwigValueInit() {
  return T();
}
#endif

/* -----------------------------------------------------------------------------
 *  This section contains generic SWIG labels for method/variable
 *  declarations/attributes, and other compiler dependent labels.
 * ----------------------------------------------------------------------------- */

/* template workaround for compilers that cannot correctly implement the C++ standard */
#ifndef SWIGTEMPLATEDISAMBIGUATOR
# if defined(__SUNPRO_CC) && (__SUNPRO_CC <= 0x560)
#  define SWIGTEMPLATEDISAMBIGUATOR template
# elif defined(__HP_aCC)
/* Needed even with `aCC -AA' when `aCC -V' reports HP ANSI C++ B3910B A.03.55 */
/* If we find a maximum version that requires this, the test would be __HP_aCC <= 35500 for A.03.55 */
#  define SWIGTEMPLATEDISAMBIGUATOR template
# else
#  define SWIGTEMPLATEDISAMBIGUATOR
# endif
#endif

/* inline attribute */
#ifndef SWIGINLINE
# if defined(__cplusplus) || (defined(__GNUC__) && !defined(__STRICT_ANSI__))
#   define SWIGINLINE inline
# else
#   define SWIGINLINE
# endif
#endif

/* attribute recognised by some compilers to avoid 'unused' warnings */
#ifndef SWIGUNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define SWIGUNUSED __attribute__ ((__unused__))
#   else
#     define SWIGUNUSED
#   endif
# elif defined(__ICC)
#   define SWIGUNUSED __attribute__ ((__unused__))
# else
#   define SWIGUNUSED
# endif
#endif

#ifndef SWIG_MSC_UNSUPPRESS_4505
# if defined(_MSC_VER)
#   pragma warning(disable : 4505) /* unreferenced local function has been removed */
# endif
#endif

#ifndef SWIGUNUSEDPARM
# ifdef __cplusplus
#   define SWIGUNUSEDPARM(p)
# else
#   define SWIGUNUSEDPARM(p) p SWIGUNUSED
# endif
#endif

/* internal SWIG method */
#ifndef SWIGINTERN
# define SWIGINTERN static SWIGUNUSED
#endif

/* internal inline SWIG method */
#ifndef SWIGINTERNINLINE
# define SWIGINTERNINLINE SWIGINTERN SWIGINLINE
#endif

/* exporting methods */
#if defined(__GNUC__)
#  if (__GNUC__ >= 4) || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)
#    ifndef GCC_HASCLASSVISIBILITY
#      define GCC_HASCLASSVISIBILITY
#    endif
#  endif
#endif

#ifndef SWIGEXPORT
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   if defined(STATIC_LINKED)
#     define SWIGEXPORT
#   else
#     define SWIGEXPORT __declspec(dllexport)
#   endif
# else
#   if defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
#     define SWIGEXPORT __attribute__ ((visibility("default")))
#   else
#     define SWIGEXPORT
#   endif
# endif
#endif

/* calling conventions for Windows */
#ifndef SWIGSTDCALL
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   define SWIGSTDCALL __stdcall
# else
#   define SWIGSTDCALL
# endif
#endif

/* Deal with Microsoft's attempt at deprecating C standard runtime functions */
#if !defined(SWIG_NO_CRT_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_CRT_SECURE_NO_DEPRECATE)
# define _CRT_SECURE_NO_DEPRECATE
#endif

/* Deal with Microsoft's attempt at deprecating methods in the standard C++ library */
#if !defined(SWIG_NO_SCL_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_SCL_SECURE_NO_DEPRECATE)
# define _SCL_SECURE_NO_DEPRECATE
#endif

/* Deal with Apple's deprecated 'AssertMacros.h' from Carbon-framework */
#if defined(__APPLE__) && !defined(__ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES)
# define __ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES 0
#endif

/* Intel's compiler complains if a variable which was never initialised is
 * cast to void, which is a common idiom which we use to indicate that we
 * are aware a variable isn't used.  So we just silence that warning.
 * See: https://github.com/swig/swig/issues/192 for more discussion.
 */
#ifdef __INTEL_COMPILER
# pragma warning disable 592
#endif


#include <stdlib.h>
#include <string.h>
#include <stdio.h>


/* Support for throwing C# exceptions from C/C++. There are two types: 
 * Exceptions that take a message and ArgumentExceptions that take a message and a parameter name. */
typedef enum {
  SWIG_CSharpApplicationException,
  SWIG_CSharpArithmeticException,
  SWIG_CSharpDivideByZeroException,
  SWIG_CSharpIndexOutOfRangeException,
  SWIG_CSharpInvalidCastException,
  SWIG_CSharpInvalidOperationException,
  SWIG_CSharpIOException,
  SWIG_CSharpNullReferenceException,
  SWIG_CSharpOutOfMemoryException,
  SWIG_CSharpOverflowException,
  SWIG_CSharpSystemException
} SWIG_CSharpExceptionCodes;

typedef enum {
  SWIG_CSharpArgumentException,
  SWIG_CSharpArgumentNullException,
  SWIG_CSharpArgumentOutOfRangeException
} SWIG_CSharpExceptionArgumentCodes;

typedef void (SWIGSTDCALL* SWIG_CSharpExceptionCallback_t)(const char *);
typedef void (SWIGSTDCALL* SWIG_CSharpExceptionArgumentCallback_t)(const char *, const char *);

typedef struct {
  SWIG_CSharpExceptionCodes code;
  SWIG_CSharpExceptionCallback_t callback;
} SWIG_CSharpException_t;

typedef struct {
  SWIG_CSharpExceptionArgumentCodes code;
  SWIG_CSharpExceptionArgumentCallback_t callback;
} SWIG_CSharpExceptionArgument_t;

static SWIG_CSharpException_t SWIG_csharp_exceptions[] = {
  { SWIG_CSharpApplicationException, NULL },
  { SWIG_CSharpArithmeticException, NULL },
  { SWIG_CSharpDivideByZeroException, NULL },
  { SWIG_CSharpIndexOutOfRangeException, NULL },
  { SWIG_CSharpInvalidCastException, NULL },
  { SWIG_CSharpInvalidOperationException, NULL },
  { SWIG_CSharpIOException, NULL },
  { SWIG_CSharpNullReferenceException, NULL },
  { SWIG_CSharpOutOfMemoryException, NULL },
  { SWIG_CSharpOverflowException, NULL },
  { SWIG_CSharpSystemException, NULL }
};

static SWIG_CSharpExceptionArgument_t SWIG_csharp_exceptions_argument[] = {
  { SWIG_CSharpArgumentException, NULL },
  { SWIG_CSharpArgumentNullException, NULL },
  { SWIG_CSharpArgumentOutOfRangeException, NULL }
};

static void SWIGUNUSED SWIG_CSharpSetPendingException(SWIG_CSharpExceptionCodes code, const char *msg) {
  SWIG_CSharpExceptionCallback_t callback = SWIG_csharp_exceptions[SWIG_CSharpApplicationException].callback;
  if ((size_t)code < sizeof(SWIG_csharp_exceptions)/sizeof(SWIG_CSharpException_t)) {
    callback = SWIG_csharp_exceptions[code].callback;
  }
  callback(msg);
}

static void SWIGUNUSED SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpExceptionArgumentCodes code, const char *msg, const char *param_name) {
  SWIG_CSharpExceptionArgumentCallback_t callback = SWIG_csharp_exceptions_argument[SWIG_CSharpArgumentException].callback;
  if ((size_t)code < sizeof(SWIG_csharp_exceptions_argument)/sizeof(SWIG_CSharpExceptionArgument_t)) {
    callback = SWIG_csharp_exceptions_argument[code].callback;
  }
  callback(msg, param_name);
}


#ifdef __cplusplus
extern "C" 
#endif
SWIGEXPORT void SWIGSTDCALL SWIGRegisterExceptionCallbacks_DatasmithFacadeCSharp(
                                                SWIG_CSharpExceptionCallback_t applicationCallback,
                                                SWIG_CSharpExceptionCallback_t arithmeticCallback,
                                                SWIG_CSharpExceptionCallback_t divideByZeroCallback, 
                                                SWIG_CSharpExceptionCallback_t indexOutOfRangeCallback, 
                                                SWIG_CSharpExceptionCallback_t invalidCastCallback,
                                                SWIG_CSharpExceptionCallback_t invalidOperationCallback,
                                                SWIG_CSharpExceptionCallback_t ioCallback,
                                                SWIG_CSharpExceptionCallback_t nullReferenceCallback,
                                                SWIG_CSharpExceptionCallback_t outOfMemoryCallback, 
                                                SWIG_CSharpExceptionCallback_t overflowCallback, 
                                                SWIG_CSharpExceptionCallback_t systemCallback) {
  SWIG_csharp_exceptions[SWIG_CSharpApplicationException].callback = applicationCallback;
  SWIG_csharp_exceptions[SWIG_CSharpArithmeticException].callback = arithmeticCallback;
  SWIG_csharp_exceptions[SWIG_CSharpDivideByZeroException].callback = divideByZeroCallback;
  SWIG_csharp_exceptions[SWIG_CSharpIndexOutOfRangeException].callback = indexOutOfRangeCallback;
  SWIG_csharp_exceptions[SWIG_CSharpInvalidCastException].callback = invalidCastCallback;
  SWIG_csharp_exceptions[SWIG_CSharpInvalidOperationException].callback = invalidOperationCallback;
  SWIG_csharp_exceptions[SWIG_CSharpIOException].callback = ioCallback;
  SWIG_csharp_exceptions[SWIG_CSharpNullReferenceException].callback = nullReferenceCallback;
  SWIG_csharp_exceptions[SWIG_CSharpOutOfMemoryException].callback = outOfMemoryCallback;
  SWIG_csharp_exceptions[SWIG_CSharpOverflowException].callback = overflowCallback;
  SWIG_csharp_exceptions[SWIG_CSharpSystemException].callback = systemCallback;
}

#ifdef __cplusplus
extern "C" 
#endif
SWIGEXPORT void SWIGSTDCALL SWIGRegisterExceptionArgumentCallbacks_DatasmithFacadeCSharp(
                                                SWIG_CSharpExceptionArgumentCallback_t argumentCallback,
                                                SWIG_CSharpExceptionArgumentCallback_t argumentNullCallback,
                                                SWIG_CSharpExceptionArgumentCallback_t argumentOutOfRangeCallback) {
  SWIG_csharp_exceptions_argument[SWIG_CSharpArgumentException].callback = argumentCallback;
  SWIG_csharp_exceptions_argument[SWIG_CSharpArgumentNullException].callback = argumentNullCallback;
  SWIG_csharp_exceptions_argument[SWIG_CSharpArgumentOutOfRangeException].callback = argumentOutOfRangeCallback;
}


/* Callback for returning strings to C# without leaking memory */
typedef char * (SWIGSTDCALL* SWIG_CSharpStringHelperCallback)(const char *);
static SWIG_CSharpStringHelperCallback SWIG_csharp_string_callback = NULL;


#ifdef __cplusplus
extern "C" 
#endif
SWIGEXPORT void SWIGSTDCALL SWIGRegisterStringCallback_DatasmithFacadeCSharp(SWIG_CSharpStringHelperCallback callback) {
  SWIG_csharp_string_callback = callback;
}


/* Contract support */

#define SWIG_contract_assert(nullreturn, expr, msg) if (!(expr)) {SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentOutOfRangeException, msg, ""); return nullreturn; } else


/* Callback for returning strings to C# without leaking memory */
typedef void * (SWIGSTDCALL* SWIG_CSharpWStringHelperCallback)(const wchar_t *);
static SWIG_CSharpWStringHelperCallback SWIG_csharp_wstring_callback = NULL;


#ifdef __cplusplus
extern "C"
#endif
SWIGEXPORT void SWIGSTDCALL SWIGRegisterWStringCallback_DatasmithFacadeCSharp(SWIG_CSharpWStringHelperCallback callback) {
  SWIG_csharp_wstring_callback = callback;
}


#include "DatasmithFacadeElement.h"
#include "DatasmithFacadeKeyValueProperty.h"
#include "DatasmithFacadeTexture.h"
#include "DatasmithFacadeMaterial.h"
#include "DatasmithFacadeMaterialID.h"
#include "DatasmithFacadeUEPbrMaterial.h"
#include "DatasmithFacadeMaterialsUtils.h"
#include "DatasmithFacadeMesh.h"
#include "DatasmithFacadeMetaData.h"
#include "DatasmithFacadeActor.h"
#include "DirectLink/DatasmithFacadeEndpointObserver.h"
#include "DirectLink/DatasmithFacadeDirectLink.h"
#include "DatasmithFacadeActorCamera.h"
#include "DatasmithFacadeActorLight.h"
#include "DatasmithFacadeActorMesh.h"
#include "DatasmithFacadeAnimation.h"
#include "DatasmithFacadeScene.h"
#include "DatasmithFacadeVariant.h"
#include "DatasmithFacadeLog.h"
#include "DatasmithFacadeUtils.h"
#include "DatasmithFacadeDecal.h"

#include "IDatasmithExporterUIModule.h"
#include "IDirectLinkUI.h"


#if PLATFORM_MAC
//wchar_t size is platform dependent, we need to make sure the size stays 16-bit, this should cover all our use-cases.
#define wchar_t char16_t
#endif


#ifdef __cplusplus
extern "C" {
#endif

SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeGuid(void * jarg1) {
  FGuid *arg1 = (FGuid *) 0 ;
  
  arg1 = (FGuid *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMessageAddress(void * jarg1) {
  FMessageAddress *arg1 = (FMessageAddress *) 0 ;
  
  arg1 = (FMessageAddress *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeElement_SetCoordinateSystemType(int jarg1) {
  FDatasmithFacadeElement::ECoordinateSystemType arg1 ;
  
  arg1 = (FDatasmithFacadeElement::ECoordinateSystemType)jarg1; 
  FDatasmithFacadeElement::SetCoordinateSystemType(arg1);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeElement_SetWorldUnitScale(float jarg1) {
  float arg1 ;
  
  arg1 = (float)jarg1; 
  FDatasmithFacadeElement::SetWorldUnitScale(arg1);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeElement(void * jarg1) {
  FDatasmithFacadeElement *arg1 = (FDatasmithFacadeElement *) 0 ;
  
  arg1 = (FDatasmithFacadeElement *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeElement_InternalGetStringHash(wchar_t * jarg1, void * jarg2, unsigned long jarg3) {
  TCHAR *arg1 = (TCHAR *) 0 ;
  TCHAR *arg2 ;
  size_t arg3 ;
  
  arg1 = (TCHAR *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (size_t)jarg3; 
  FDatasmithFacadeElement::GetStringHash((wchar_t const *)arg1,arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeElement_SetName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeElement *arg1 = (FDatasmithFacadeElement *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeElement *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetName((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeElement_GetName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeElement *arg1 = (FDatasmithFacadeElement *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeElement *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeElement const *)arg1)->GetName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeElement_SetLabel(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeElement *arg1 = (FDatasmithFacadeElement *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeElement *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetLabel((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeElement_GetLabel(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeElement *arg1 = (FDatasmithFacadeElement *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeElement *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeElement const *)arg1)->GetLabel();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeKeyValueProperty(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeKeyValueProperty *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeKeyValueProperty *)new FDatasmithFacadeKeyValueProperty((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeKeyValueProperty(void * jarg1) {
  FDatasmithFacadeKeyValueProperty *arg1 = (FDatasmithFacadeKeyValueProperty *) 0 ;
  
  arg1 = (FDatasmithFacadeKeyValueProperty *)jarg1; 
  delete arg1;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeKeyValueProperty_GetPropertyType(void * jarg1) {
  int jresult ;
  FDatasmithFacadeKeyValueProperty *arg1 = (FDatasmithFacadeKeyValueProperty *) 0 ;
  FDatasmithFacadeKeyValueProperty::EKeyValuePropertyType result;
  
  arg1 = (FDatasmithFacadeKeyValueProperty *)jarg1; 
  result = (FDatasmithFacadeKeyValueProperty::EKeyValuePropertyType)((FDatasmithFacadeKeyValueProperty const *)arg1)->GetPropertyType();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeKeyValueProperty_SetPropertyType(void * jarg1, int jarg2) {
  FDatasmithFacadeKeyValueProperty *arg1 = (FDatasmithFacadeKeyValueProperty *) 0 ;
  FDatasmithFacadeKeyValueProperty::EKeyValuePropertyType arg2 ;
  
  arg1 = (FDatasmithFacadeKeyValueProperty *)jarg1; 
  arg2 = (FDatasmithFacadeKeyValueProperty::EKeyValuePropertyType)jarg2; 
  (arg1)->SetPropertyType(arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeKeyValueProperty_GetValue(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeKeyValueProperty *arg1 = (FDatasmithFacadeKeyValueProperty *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeKeyValueProperty *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeKeyValueProperty const *)arg1)->GetValue();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeKeyValueProperty_SetValue(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeKeyValueProperty *arg1 = (FDatasmithFacadeKeyValueProperty *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeKeyValueProperty *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetValue((TCHAR const *)arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeTexture(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeTexture *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeTexture *)new FDatasmithFacadeTexture((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeTexture(void * jarg1) {
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  delete arg1;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeTexture_GetFile(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeTexture const *)arg1)->GetFile();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTexture_SetFile(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetFile((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTexture_SetData(void * jarg1, void * jarg2, unsigned int jarg3, int jarg4) {
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  uint8 *arg2 = (uint8 *) 0 ;
  uint32 arg3 ;
  FDatasmithFacadeTexture::ETextureFormat arg4 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  arg2 = (uint8 *)jarg2; 
  arg3 = (uint32)jarg3; 
  arg4 = (FDatasmithFacadeTexture::ETextureFormat)jarg4; 
  (arg1)->SetData((uint8 const *)arg2,arg3,arg4);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeTexture_GetData(void * jarg1, unsigned int * jarg2, int * jarg3) {
  void * jresult ;
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  uint32 *arg2 = 0 ;
  FDatasmithFacadeTexture::ETextureFormat *arg3 = 0 ;
  uint8 *result = 0 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  arg2 = (uint32 *)jarg2; 
  arg3 = (FDatasmithFacadeTexture::ETextureFormat *)jarg3;
  if (!arg3) {
    SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentNullException, "FDatasmithFacadeTexture::ETextureFormat & type is null", 0);
    return 0;
  } 
  result = (uint8 *)((FDatasmithFacadeTexture const *)arg1)->GetData(*arg2,*arg3);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTexture_InternalGetFileHash(void * jarg1, void * jarg2, unsigned long jarg3) {
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  TCHAR *arg2 ;
  size_t arg3 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (size_t)jarg3; 
  ((FDatasmithFacadeTexture const *)arg1)->GetFileHash(arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTexture_SetFileHash(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetFileHash((TCHAR const *)arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeTexture_GetTextureMode(void * jarg1) {
  int jresult ;
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  FDatasmithFacadeTexture::ETextureMode result;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  result = (FDatasmithFacadeTexture::ETextureMode)((FDatasmithFacadeTexture const *)arg1)->GetTextureMode();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTexture_SetTextureMode(void * jarg1, int jarg2) {
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  FDatasmithFacadeTexture::ETextureMode arg2 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  arg2 = (FDatasmithFacadeTexture::ETextureMode)jarg2; 
  (arg1)->SetTextureMode(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeTexture_GetTextureFilter(void * jarg1) {
  int jresult ;
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  FDatasmithFacadeTexture::ETextureFilter result;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  result = (FDatasmithFacadeTexture::ETextureFilter)((FDatasmithFacadeTexture const *)arg1)->GetTextureFilter();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTexture_SetTextureFilter(void * jarg1, int jarg2) {
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  FDatasmithFacadeTexture::ETextureFilter arg2 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  arg2 = (FDatasmithFacadeTexture::ETextureFilter)jarg2; 
  (arg1)->SetTextureFilter(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeTexture_GetTextureAddressX(void * jarg1) {
  int jresult ;
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  FDatasmithFacadeTexture::ETextureAddress result;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  result = (FDatasmithFacadeTexture::ETextureAddress)((FDatasmithFacadeTexture const *)arg1)->GetTextureAddressX();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTexture_SetTextureAddressX(void * jarg1, int jarg2) {
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  FDatasmithFacadeTexture::ETextureAddress arg2 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  arg2 = (FDatasmithFacadeTexture::ETextureAddress)jarg2; 
  (arg1)->SetTextureAddressX(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeTexture_GetTextureAddressY(void * jarg1) {
  int jresult ;
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  FDatasmithFacadeTexture::ETextureAddress result;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  result = (FDatasmithFacadeTexture::ETextureAddress)((FDatasmithFacadeTexture const *)arg1)->GetTextureAddressY();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTexture_SetTextureAddressY(void * jarg1, int jarg2) {
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  FDatasmithFacadeTexture::ETextureAddress arg2 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  arg2 = (FDatasmithFacadeTexture::ETextureAddress)jarg2; 
  (arg1)->SetTextureAddressY(arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeTexture_GetAllowResize(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  result = (bool)((FDatasmithFacadeTexture const *)arg1)->GetAllowResize();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTexture_SetAllowResize(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetAllowResize(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeTexture_GetRGBCurve(void * jarg1) {
  float jresult ;
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  result = (float)((FDatasmithFacadeTexture const *)arg1)->GetRGBCurve();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTexture_SetRGBCurve(void * jarg1, float jarg2) {
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetRGBCurve(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeTexture_GetSRGB(void * jarg1) {
  int jresult ;
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  FDatasmithFacadeTexture::EColorSpace result;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  result = (FDatasmithFacadeTexture::EColorSpace)((FDatasmithFacadeTexture const *)arg1)->GetSRGB();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTexture_SetSRGB(void * jarg1, int jarg2) {
  FDatasmithFacadeTexture *arg1 = (FDatasmithFacadeTexture *) 0 ;
  FDatasmithFacadeTexture::EColorSpace arg2 ;
  
  arg1 = (FDatasmithFacadeTexture *)jarg1; 
  arg2 = (FDatasmithFacadeTexture::EColorSpace)jarg2; 
  (arg1)->SetSRGB(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeBaseMaterial_GetDatasmithMaterialType(void * jarg1) {
  int jresult ;
  FDatasmithFacadeBaseMaterial *arg1 = (FDatasmithFacadeBaseMaterial *) 0 ;
  FDatasmithFacadeBaseMaterial::EDatasmithMaterialType result;
  
  arg1 = (FDatasmithFacadeBaseMaterial *)jarg1; 
  result = (FDatasmithFacadeBaseMaterial::EDatasmithMaterialType)((FDatasmithFacadeBaseMaterial const *)arg1)->GetDatasmithMaterialType();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeBaseMaterial(void * jarg1) {
  FDatasmithFacadeBaseMaterial *arg1 = (FDatasmithFacadeBaseMaterial *) 0 ;
  
  arg1 = (FDatasmithFacadeBaseMaterial *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeMaterialInstance(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeMaterialInstance *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeMaterialInstance *)new FDatasmithFacadeMaterialInstance((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialInstance(void * jarg1) {
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  delete arg1;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_GetMaterialType(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  FDatasmithFacadeMaterialInstance::EMaterialInstanceType result;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  result = (FDatasmithFacadeMaterialInstance::EMaterialInstanceType)((FDatasmithFacadeMaterialInstance const *)arg1)->GetMaterialType();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_SetMaterialType(void * jarg1, int jarg2) {
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  FDatasmithFacadeMaterialInstance::EMaterialInstanceType arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  arg2 = (FDatasmithFacadeMaterialInstance::EMaterialInstanceType)jarg2; 
  (arg1)->SetMaterialType(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_GetQuality(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  FDatasmithFacadeMaterialInstance::EMaterialInstanceQuality result;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  result = (FDatasmithFacadeMaterialInstance::EMaterialInstanceQuality)((FDatasmithFacadeMaterialInstance const *)arg1)->GetQuality();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_SetQuality(void * jarg1, int jarg2) {
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  FDatasmithFacadeMaterialInstance::EMaterialInstanceQuality arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  arg2 = (FDatasmithFacadeMaterialInstance::EMaterialInstanceQuality)jarg2; 
  (arg1)->SetQuality(arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_GetCustomMaterialPathName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeMaterialInstance const *)arg1)->GetCustomMaterialPathName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_SetCustomMaterialPathName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetCustomMaterialPathName((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_AddColor__SWIG_0(void * jarg1, wchar_t * jarg2, unsigned char jarg3, unsigned char jarg4, unsigned char jarg5, unsigned char jarg6) {
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  unsigned char arg3 ;
  unsigned char arg4 ;
  unsigned char arg5 ;
  unsigned char arg6 ;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (unsigned char)jarg3; 
  arg4 = (unsigned char)jarg4; 
  arg5 = (unsigned char)jarg5; 
  arg6 = (unsigned char)jarg6; 
  (arg1)->AddColor((TCHAR const *)arg2,arg3,arg4,arg5,arg6);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_AddColor__SWIG_1(void * jarg1, wchar_t * jarg2, float jarg3, float jarg4, float jarg5, float jarg6) {
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  float arg3 ;
  float arg4 ;
  float arg5 ;
  float arg6 ;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (float)jarg3; 
  arg4 = (float)jarg4; 
  arg5 = (float)jarg5; 
  arg6 = (float)jarg6; 
  (arg1)->AddColor((TCHAR const *)arg2,arg3,arg4,arg5,arg6);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_AddTexture(void * jarg1, wchar_t * jarg2, void * jarg3) {
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  FDatasmithFacadeTexture *arg3 = (FDatasmithFacadeTexture *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (FDatasmithFacadeTexture *)jarg3; 
  (arg1)->AddTexture((TCHAR const *)arg2,(FDatasmithFacadeTexture const *)arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_AddString(void * jarg1, wchar_t * jarg2, wchar_t * jarg3) {
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  TCHAR *arg3 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (TCHAR *)jarg3; 
  (arg1)->AddString((TCHAR const *)arg2,(TCHAR const *)arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_AddFloat(void * jarg1, wchar_t * jarg2, float jarg3) {
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  float arg3 ;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (float)jarg3; 
  (arg1)->AddFloat((TCHAR const *)arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_AddBoolean(void * jarg1, wchar_t * jarg2, unsigned int jarg3) {
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  bool arg3 ;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = jarg3 ? true : false; 
  (arg1)->AddBoolean((TCHAR const *)arg2,arg3);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_GetPropertiesCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  result = (int32)((FDatasmithFacadeMaterialInstance const *)arg1)->GetPropertiesCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_GetProperty(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeKeyValueProperty *result = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeKeyValueProperty *)((FDatasmithFacadeMaterialInstance const *)arg1)->GetNewProperty(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_GetPropertyByName(void * jarg1, wchar_t * jarg2) {
  void * jresult ;
  FDatasmithFacadeMaterialInstance *arg1 = (FDatasmithFacadeMaterialInstance *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  FDatasmithFacadeKeyValueProperty *result = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialInstance *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  result = (FDatasmithFacadeKeyValueProperty *)((FDatasmithFacadeMaterialInstance const *)arg1)->GetNewPropertyByName((TCHAR const *)arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeMaterialID(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeMaterialID *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeMaterialID *)new FDatasmithFacadeMaterialID((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialID(void * jarg1) {
  FDatasmithFacadeMaterialID *arg1 = (FDatasmithFacadeMaterialID *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialID *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialID_SetId(void * jarg1, int jarg2) {
  FDatasmithFacadeMaterialID *arg1 = (FDatasmithFacadeMaterialID *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialID *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->SetId(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMaterialID_GetId(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMaterialID *arg1 = (FDatasmithFacadeMaterialID *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMaterialID *)jarg1; 
  result = (int32)((FDatasmithFacadeMaterialID const *)arg1)->GetId();
  jresult = result; 
  return jresult;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeExpressionInput_GetName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeExpressionInput *arg1 = (FDatasmithFacadeExpressionInput *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeExpressionInput *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeExpressionInput const *)arg1)->GetName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeExpressionInput_SetName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeExpressionInput *arg1 = (FDatasmithFacadeExpressionInput *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeExpressionInput *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetName((TCHAR const *)arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeExpressionInput_GetExpression(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeExpressionInput *arg1 = (FDatasmithFacadeExpressionInput *) 0 ;
  FDatasmithFacadeMaterialExpression *result = 0 ;
  
  arg1 = (FDatasmithFacadeExpressionInput *)jarg1; 
  result = (FDatasmithFacadeMaterialExpression *)(arg1)->GetNewFacadeExpression();
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeExpressionInput_SetExpression(void * jarg1, void * jarg2) {
  FDatasmithFacadeExpressionInput *arg1 = (FDatasmithFacadeExpressionInput *) 0 ;
  FDatasmithFacadeMaterialExpression *arg2 = (FDatasmithFacadeMaterialExpression *) 0 ;
  
  arg1 = (FDatasmithFacadeExpressionInput *)jarg1; 
  arg2 = (FDatasmithFacadeMaterialExpression *)jarg2; 
  (arg1)->SetExpression(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeExpressionInput_GetOutputIndex(void * jarg1) {
  int jresult ;
  FDatasmithFacadeExpressionInput *arg1 = (FDatasmithFacadeExpressionInput *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeExpressionInput *)jarg1; 
  result = (int32)((FDatasmithFacadeExpressionInput const *)arg1)->GetOutputIndex();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeExpressionInput_SetOutputIndex(void * jarg1, int jarg2) {
  FDatasmithFacadeExpressionInput *arg1 = (FDatasmithFacadeExpressionInput *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeExpressionInput *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->SetOutputIndex(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeExpressionInput(void * jarg1) {
  FDatasmithFacadeExpressionInput *arg1 = (FDatasmithFacadeExpressionInput *) 0 ;
  
  arg1 = (FDatasmithFacadeExpressionInput *)jarg1; 
  delete arg1;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpression_GetName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeMaterialExpression *arg1 = (FDatasmithFacadeMaterialExpression *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpression *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeMaterialExpression const *)arg1)->GetName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpression_SetName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeMaterialExpression *arg1 = (FDatasmithFacadeMaterialExpression *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpression *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetName((TCHAR const *)arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpression_GetExpressionType(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMaterialExpression *arg1 = (FDatasmithFacadeMaterialExpression *) 0 ;
  EDatasmithFacadeMaterialExpressionType result;
  
  arg1 = (FDatasmithFacadeMaterialExpression *)jarg1; 
  result = (EDatasmithFacadeMaterialExpressionType)((FDatasmithFacadeMaterialExpression const *)arg1)->GetExpressionType();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpression_ConnectExpression__SWIG_0(void * jarg1, void * jarg2) {
  FDatasmithFacadeMaterialExpression *arg1 = (FDatasmithFacadeMaterialExpression *) 0 ;
  FDatasmithFacadeExpressionInput *arg2 = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpression *)jarg1; 
  arg2 = (FDatasmithFacadeExpressionInput *)jarg2;
  if (!arg2) {
    SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentNullException, "FDatasmithFacadeExpressionInput & type is null", 0);
    return ;
  } 
  (arg1)->ConnectExpression(*arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpression_ConnectExpression__SWIG_1(void * jarg1, void * jarg2, int jarg3) {
  FDatasmithFacadeMaterialExpression *arg1 = (FDatasmithFacadeMaterialExpression *) 0 ;
  FDatasmithFacadeExpressionInput *arg2 = 0 ;
  int32 arg3 ;
  
  arg1 = (FDatasmithFacadeMaterialExpression *)jarg1; 
  arg2 = (FDatasmithFacadeExpressionInput *)jarg2;
  if (!arg2) {
    SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentNullException, "FDatasmithFacadeExpressionInput & type is null", 0);
    return ;
  } 
  arg3 = (int32)jarg3; 
  (arg1)->ConnectExpression(*arg2,arg3);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpression_GetInputCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMaterialExpression *arg1 = (FDatasmithFacadeMaterialExpression *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMaterialExpression *)jarg1; 
  result = (int32)((FDatasmithFacadeMaterialExpression const *)arg1)->GetInputCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpression_GetInput(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeMaterialExpression *arg1 = (FDatasmithFacadeMaterialExpression *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeExpressionInput *result = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpression *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeExpressionInput *)(arg1)->GetNewFacadeInput(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpression_GetDefaultOutputIndex(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMaterialExpression *arg1 = (FDatasmithFacadeMaterialExpression *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMaterialExpression *)jarg1; 
  result = (int32)((FDatasmithFacadeMaterialExpression const *)arg1)->GetDefaultOutputIndex();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpression_SetDefaultOutputIndex(void * jarg1, int jarg2) {
  FDatasmithFacadeMaterialExpression *arg1 = (FDatasmithFacadeMaterialExpression *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialExpression *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->SetDefaultOutputIndex(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialExpression(void * jarg1) {
  FDatasmithFacadeMaterialExpression *arg1 = (FDatasmithFacadeMaterialExpression *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpression *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpression_ResetExpression(void * jarg1) {
  FDatasmithFacadeMaterialExpression *arg1 = (FDatasmithFacadeMaterialExpression *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpression *)jarg1; 
  (arg1)->ResetExpression();
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeExpressionParameterSwigImpl(void * jarg1) {
  FDatasmithFacadeExpressionParameter *arg1 = (FDatasmithFacadeExpressionParameter *) 0 ;
  
  arg1 = (FDatasmithFacadeExpressionParameter *)jarg1; 
  delete arg1;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeExpressionParameterSwigImpl_GetGroupName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeExpressionParameter *arg1 = (FDatasmithFacadeExpressionParameter *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeExpressionParameter *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeExpressionParameter const *)arg1)->GetGroupName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeExpressionParameterSwigImpl_SetGroupName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeExpressionParameter *arg1 = (FDatasmithFacadeExpressionParameter *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeExpressionParameter *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetGroupName((TCHAR const *)arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionBool_GetBool(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeMaterialExpressionBool *arg1 = (FDatasmithFacadeMaterialExpressionBool *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeMaterialExpressionBool *)jarg1; 
  result = (bool)((FDatasmithFacadeMaterialExpressionBool const *)arg1)->GetBool();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionBool_SetBool(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeMaterialExpressionBool *arg1 = (FDatasmithFacadeMaterialExpressionBool *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionBool *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetBool(arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionBool_GetGroupName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeMaterialExpressionBool *arg1 = (FDatasmithFacadeMaterialExpressionBool *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionBool *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeMaterialExpressionBool const *)arg1)->GetGroupName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionBool_SetGroupName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeMaterialExpressionBool *arg1 = (FDatasmithFacadeMaterialExpressionBool *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionBool *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetGroupName((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialExpressionBool(void * jarg1) {
  FDatasmithFacadeMaterialExpressionBool *arg1 = (FDatasmithFacadeMaterialExpressionBool *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionBool *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionColor_GetsRGBColor(void * jarg1, unsigned char * jarg2, unsigned char * jarg3, unsigned char * jarg4, unsigned char * jarg5) {
  FDatasmithFacadeMaterialExpressionColor *arg1 = (FDatasmithFacadeMaterialExpressionColor *) 0 ;
  uint8 *arg2 = 0 ;
  uint8 *arg3 = 0 ;
  uint8 *arg4 = 0 ;
  uint8 *arg5 = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionColor *)jarg1; 
  arg2 = (uint8 *)jarg2; 
  arg3 = (uint8 *)jarg3; 
  arg4 = (uint8 *)jarg4; 
  arg5 = (uint8 *)jarg5; 
  ((FDatasmithFacadeMaterialExpressionColor const *)arg1)->GetsRGBColor(*arg2,*arg3,*arg4,*arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionColor_SetsRGBColor(void * jarg1, unsigned char jarg2, unsigned char jarg3, unsigned char jarg4, unsigned char jarg5) {
  FDatasmithFacadeMaterialExpressionColor *arg1 = (FDatasmithFacadeMaterialExpressionColor *) 0 ;
  uint8 arg2 ;
  uint8 arg3 ;
  uint8 arg4 ;
  uint8 arg5 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionColor *)jarg1; 
  arg2 = (uint8)jarg2; 
  arg3 = (uint8)jarg3; 
  arg4 = (uint8)jarg4; 
  arg5 = (uint8)jarg5; 
  (arg1)->SetsRGBColor(arg2,arg3,arg4,arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionColor_GetColor(void * jarg1, float * jarg2, float * jarg3, float * jarg4, float * jarg5) {
  FDatasmithFacadeMaterialExpressionColor *arg1 = (FDatasmithFacadeMaterialExpressionColor *) 0 ;
  float *arg2 = 0 ;
  float *arg3 = 0 ;
  float *arg4 = 0 ;
  float *arg5 = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionColor *)jarg1; 
  arg2 = (float *)jarg2; 
  arg3 = (float *)jarg3; 
  arg4 = (float *)jarg4; 
  arg5 = (float *)jarg5; 
  ((FDatasmithFacadeMaterialExpressionColor const *)arg1)->GetColor(*arg2,*arg3,*arg4,*arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionColor_SetColor(void * jarg1, float jarg2, float jarg3, float jarg4, float jarg5) {
  FDatasmithFacadeMaterialExpressionColor *arg1 = (FDatasmithFacadeMaterialExpressionColor *) 0 ;
  float arg2 ;
  float arg3 ;
  float arg4 ;
  float arg5 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionColor *)jarg1; 
  arg2 = (float)jarg2; 
  arg3 = (float)jarg3; 
  arg4 = (float)jarg4; 
  arg5 = (float)jarg5; 
  (arg1)->SetColor(arg2,arg3,arg4,arg5);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionColor_GetGroupName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeMaterialExpressionColor *arg1 = (FDatasmithFacadeMaterialExpressionColor *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionColor *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeMaterialExpressionColor const *)arg1)->GetGroupName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionColor_SetGroupName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeMaterialExpressionColor *arg1 = (FDatasmithFacadeMaterialExpressionColor *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionColor *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetGroupName((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialExpressionColor(void * jarg1) {
  FDatasmithFacadeMaterialExpressionColor *arg1 = (FDatasmithFacadeMaterialExpressionColor *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionColor *)jarg1; 
  delete arg1;
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionScalar_GetScalar(void * jarg1) {
  float jresult ;
  FDatasmithFacadeMaterialExpressionScalar *arg1 = (FDatasmithFacadeMaterialExpressionScalar *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeMaterialExpressionScalar *)jarg1; 
  result = (float)((FDatasmithFacadeMaterialExpressionScalar const *)arg1)->GetScalar();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionScalar_SetScalar(void * jarg1, float jarg2) {
  FDatasmithFacadeMaterialExpressionScalar *arg1 = (FDatasmithFacadeMaterialExpressionScalar *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionScalar *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetScalar(arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionScalar_GetGroupName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeMaterialExpressionScalar *arg1 = (FDatasmithFacadeMaterialExpressionScalar *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionScalar *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeMaterialExpressionScalar const *)arg1)->GetGroupName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionScalar_SetGroupName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeMaterialExpressionScalar *arg1 = (FDatasmithFacadeMaterialExpressionScalar *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionScalar *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetGroupName((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialExpressionScalar(void * jarg1) {
  FDatasmithFacadeMaterialExpressionScalar *arg1 = (FDatasmithFacadeMaterialExpressionScalar *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionScalar *)jarg1; 
  delete arg1;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTexture_GetTexturePathName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeMaterialExpressionTexture *arg1 = (FDatasmithFacadeMaterialExpressionTexture *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionTexture *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeMaterialExpressionTexture const *)arg1)->GetTexturePathName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTexture_SetTexturePathName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeMaterialExpressionTexture *arg1 = (FDatasmithFacadeMaterialExpressionTexture *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionTexture *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetTexturePathName((TCHAR const *)arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTexture_GetInputCoordinate(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeMaterialExpressionTexture *arg1 = (FDatasmithFacadeMaterialExpressionTexture *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeMaterialExpressionTexture *)jarg1; 
  result = (arg1)->GetInputCoordinate();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTexture_GetGroupName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeMaterialExpressionTexture *arg1 = (FDatasmithFacadeMaterialExpressionTexture *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionTexture *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeMaterialExpressionTexture const *)arg1)->GetGroupName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTexture_SetGroupName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeMaterialExpressionTexture *arg1 = (FDatasmithFacadeMaterialExpressionTexture *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionTexture *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetGroupName((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialExpressionTexture(void * jarg1) {
  FDatasmithFacadeMaterialExpressionTexture *arg1 = (FDatasmithFacadeMaterialExpressionTexture *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionTexture *)jarg1; 
  delete arg1;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTextureCoordinate_GetCoordinateIndex(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMaterialExpressionTextureCoordinate *arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *)jarg1; 
  result = (int32)((FDatasmithFacadeMaterialExpressionTextureCoordinate const *)arg1)->GetCoordinateIndex();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTextureCoordinate_SetCoordinateIndex(void * jarg1, int jarg2) {
  FDatasmithFacadeMaterialExpressionTextureCoordinate *arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->SetCoordinateIndex(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTextureCoordinate_GetUTiling(void * jarg1) {
  float jresult ;
  FDatasmithFacadeMaterialExpressionTextureCoordinate *arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *)jarg1; 
  result = (float)((FDatasmithFacadeMaterialExpressionTextureCoordinate const *)arg1)->GetUTiling();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTextureCoordinate_SetUTiling(void * jarg1, float jarg2) {
  FDatasmithFacadeMaterialExpressionTextureCoordinate *arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetUTiling(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTextureCoordinate_GetVTiling(void * jarg1) {
  float jresult ;
  FDatasmithFacadeMaterialExpressionTextureCoordinate *arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *)jarg1; 
  result = (float)((FDatasmithFacadeMaterialExpressionTextureCoordinate const *)arg1)->GetVTiling();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTextureCoordinate_SetVTiling(void * jarg1, float jarg2) {
  FDatasmithFacadeMaterialExpressionTextureCoordinate *arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetVTiling(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialExpressionTextureCoordinate(void * jarg1) {
  FDatasmithFacadeMaterialExpressionTextureCoordinate *arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionTextureCoordinate *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionFlattenNormal_GetNormal(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeMaterialExpressionFlattenNormal *arg1 = (FDatasmithFacadeMaterialExpressionFlattenNormal *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeMaterialExpressionFlattenNormal *)jarg1; 
  result = ((FDatasmithFacadeMaterialExpressionFlattenNormal const *)arg1)->GetNormal();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionFlattenNormal_GetFlatness(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeMaterialExpressionFlattenNormal *arg1 = (FDatasmithFacadeMaterialExpressionFlattenNormal *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeMaterialExpressionFlattenNormal *)jarg1; 
  result = ((FDatasmithFacadeMaterialExpressionFlattenNormal const *)arg1)->GetFlatness();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialExpressionFlattenNormal(void * jarg1) {
  FDatasmithFacadeMaterialExpressionFlattenNormal *arg1 = (FDatasmithFacadeMaterialExpressionFlattenNormal *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionFlattenNormal *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionGeneric_SetExpressionName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeMaterialExpressionGeneric *arg1 = (FDatasmithFacadeMaterialExpressionGeneric *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionGeneric *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetExpressionName((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionGeneric_GetExpressionName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeMaterialExpressionGeneric *arg1 = (FDatasmithFacadeMaterialExpressionGeneric *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionGeneric *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeMaterialExpressionGeneric const *)arg1)->GetExpressionName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionGeneric_GetPropertiesCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMaterialExpressionGeneric *arg1 = (FDatasmithFacadeMaterialExpressionGeneric *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMaterialExpressionGeneric *)jarg1; 
  result = (int32)((FDatasmithFacadeMaterialExpressionGeneric const *)arg1)->GetPropertiesCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionGeneric_AddProperty(void * jarg1, void * jarg2) {
  FDatasmithFacadeMaterialExpressionGeneric *arg1 = (FDatasmithFacadeMaterialExpressionGeneric *) 0 ;
  FDatasmithFacadeKeyValueProperty *arg2 = (FDatasmithFacadeKeyValueProperty *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionGeneric *)jarg1; 
  arg2 = (FDatasmithFacadeKeyValueProperty *)jarg2; 
  (arg1)->AddProperty((FDatasmithFacadeKeyValueProperty const *)arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionGeneric_GetProperty(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeMaterialExpressionGeneric *arg1 = (FDatasmithFacadeMaterialExpressionGeneric *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeKeyValueProperty *result = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionGeneric *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeKeyValueProperty *)(arg1)->GetNewProperty(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialExpressionGeneric(void * jarg1) {
  FDatasmithFacadeMaterialExpressionGeneric *arg1 = (FDatasmithFacadeMaterialExpressionGeneric *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionGeneric *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionFunctionCall_SetFunctionPathName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeMaterialExpressionFunctionCall *arg1 = (FDatasmithFacadeMaterialExpressionFunctionCall *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionFunctionCall *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetFunctionPathName((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionFunctionCall_GetFunctionPathName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeMaterialExpressionFunctionCall *arg1 = (FDatasmithFacadeMaterialExpressionFunctionCall *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionFunctionCall *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeMaterialExpressionFunctionCall const *)arg1)->GetFunctionPathName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialExpressionFunctionCall(void * jarg1) {
  FDatasmithFacadeMaterialExpressionFunctionCall *arg1 = (FDatasmithFacadeMaterialExpressionFunctionCall *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialExpressionFunctionCall *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeUEPbrMaterial(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeUEPbrMaterial *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeUEPbrMaterial *)new FDatasmithFacadeUEPbrMaterial((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeUEPbrMaterial(void * jarg1) {
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetBaseColor(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = ((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetBaseColor();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetMetallic(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = ((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetMetallic();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetSpecular(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = ((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetSpecular();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetRoughness(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = ((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetRoughness();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetEmissiveColor(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = ((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetEmissiveColor();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetOpacity(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = ((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetOpacity();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetNormal(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = ((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetNormal();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetRefraction(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = ((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetRefraction();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetAmbientOcclusion(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = ((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetAmbientOcclusion();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetClearCoat(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = ((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetClearCoat();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetClearCoatRoughness(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = ((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetClearCoatRoughness();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetWorldPositionOffset(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = ((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetWorldPositionOffset();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetMaterialAttributes(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeExpressionInput > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = ((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetMaterialAttributes();
  jresult = new FDatasmithFacadeExpressionInput((const FDatasmithFacadeExpressionInput &)result); 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetBlendMode(void * jarg1) {
  int jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  int result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (int)((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetBlendMode();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_SetBlendMode(void * jarg1, int jarg2) {
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  int arg2 ;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  arg2 = (int)jarg2; 
  (arg1)->SetBlendMode(arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetTwoSided(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (bool)((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetTwoSided();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_SetTwoSided(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetTwoSided(arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetUseMaterialAttributes(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (bool)((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetUseMaterialAttributes();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_SetUseMaterialAttributes(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetUseMaterialAttributes(arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetMaterialFunctionOnly(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (bool)((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetMaterialFunctionOnly();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_SetMaterialFunctionOnly(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetMaterialFunctionOnly(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetOpacityMaskClipValue(void * jarg1) {
  float jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (float)((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetOpacityMaskClipValue();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_SetOpacityMaskClipValue(void * jarg1, float jarg2) {
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetOpacityMaskClipValue(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetExpressionsCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (int32)((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetExpressionsCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetExpression(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeMaterialExpression *result = 0 ;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeMaterialExpression *)(arg1)->GetNewFacadeExpression(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetExpressionIndex(void * jarg1, void * jarg2) {
  int jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  FDatasmithFacadeMaterialExpression *arg2 = 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  arg2 = (FDatasmithFacadeMaterialExpression *)jarg2;
  if (!arg2) {
    SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentNullException, "FDatasmithFacadeMaterialExpression const & type is null", 0);
    return 0;
  } 
  result = (int32)((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetExpressionIndex((FDatasmithFacadeMaterialExpression const &)*arg2);
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_ResetExpressionGraph(void * jarg1) {
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  (arg1)->ResetExpressionGraph();
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_SetParentLabel(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetParentLabel((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_GetParentLabel(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeUEPbrMaterial const *)arg1)->GetParentLabel();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionBool(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeMaterialExpressionBool > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (arg1)->SWIGTEMPLATEDISAMBIGUATOR AddMaterialExpression< FDatasmithFacadeMaterialExpressionBool >();
  jresult = new FDatasmithFacadeMaterialExpressionBool((const FDatasmithFacadeMaterialExpressionBool &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionColor(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeMaterialExpressionColor > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (arg1)->SWIGTEMPLATEDISAMBIGUATOR AddMaterialExpression< FDatasmithFacadeMaterialExpressionColor >();
  jresult = new FDatasmithFacadeMaterialExpressionColor((const FDatasmithFacadeMaterialExpressionColor &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionFlattenNormal(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeMaterialExpressionFlattenNormal > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (arg1)->SWIGTEMPLATEDISAMBIGUATOR AddMaterialExpression< FDatasmithFacadeMaterialExpressionFlattenNormal >();
  jresult = new FDatasmithFacadeMaterialExpressionFlattenNormal((const FDatasmithFacadeMaterialExpressionFlattenNormal &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionFunctionCall(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeMaterialExpressionFunctionCall > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (arg1)->SWIGTEMPLATEDISAMBIGUATOR AddMaterialExpression< FDatasmithFacadeMaterialExpressionFunctionCall >();
  jresult = new FDatasmithFacadeMaterialExpressionFunctionCall((const FDatasmithFacadeMaterialExpressionFunctionCall &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionGeneric(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeMaterialExpressionGeneric > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (arg1)->SWIGTEMPLATEDISAMBIGUATOR AddMaterialExpression< FDatasmithFacadeMaterialExpressionGeneric >();
  jresult = new FDatasmithFacadeMaterialExpressionGeneric((const FDatasmithFacadeMaterialExpressionGeneric &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionScalar(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeMaterialExpressionScalar > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (arg1)->SWIGTEMPLATEDISAMBIGUATOR AddMaterialExpression< FDatasmithFacadeMaterialExpressionScalar >();
  jresult = new FDatasmithFacadeMaterialExpressionScalar((const FDatasmithFacadeMaterialExpressionScalar &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionTexture(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeMaterialExpressionTexture > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (arg1)->SWIGTEMPLATEDISAMBIGUATOR AddMaterialExpression< FDatasmithFacadeMaterialExpressionTexture >();
  jresult = new FDatasmithFacadeMaterialExpressionTexture((const FDatasmithFacadeMaterialExpressionTexture &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionTextureCoordinate(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = (FDatasmithFacadeUEPbrMaterial *) 0 ;
  SwigValueWrapper< FDatasmithFacadeMaterialExpressionTextureCoordinate > result;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1; 
  result = (arg1)->SWIGTEMPLATEDISAMBIGUATOR AddMaterialExpression< FDatasmithFacadeMaterialExpressionTextureCoordinate >();
  jresult = new FDatasmithFacadeMaterialExpressionTextureCoordinate((const FDatasmithFacadeMaterialExpressionTextureCoordinate &)result); 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_SetUVOffset(void * jarg1, float jarg2, float jarg3) {
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  float arg2 ;
  float arg3 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  arg2 = (float)jarg2; 
  arg3 = (float)jarg3; 
  (arg1)->SetUVOffset(arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_GetUVOffset(void * jarg1, float * jarg2, float * jarg3) {
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  float *arg2 = 0 ;
  float *arg3 = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  arg2 = (float *)jarg2; 
  arg3 = (float *)jarg3; 
  ((FDatasmithFacadeMaterialsUtils::FUVEditParameters const *)arg1)->GetUVOffset(*arg2,*arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_SetUVTiling(void * jarg1, float jarg2, float jarg3) {
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  float arg2 ;
  float arg3 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  arg2 = (float)jarg2; 
  arg3 = (float)jarg3; 
  (arg1)->SetUVTiling(arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_GetUVTiling(void * jarg1, float * jarg2, float * jarg3) {
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  float *arg2 = 0 ;
  float *arg3 = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  arg2 = (float *)jarg2; 
  arg3 = (float *)jarg3; 
  ((FDatasmithFacadeMaterialsUtils::FUVEditParameters const *)arg1)->GetUVTiling(*arg2,*arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_SetRotationPivot(void * jarg1, float jarg2, float jarg3, float jarg4) {
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  float arg2 ;
  float arg3 ;
  float arg4 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  arg2 = (float)jarg2; 
  arg3 = (float)jarg3; 
  arg4 = (float)jarg4; 
  (arg1)->SetRotationPivot(arg2,arg3,arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_GetRotationPivot(void * jarg1, float * jarg2, float * jarg3, float * jarg4) {
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  float *arg2 = 0 ;
  float *arg3 = 0 ;
  float *arg4 = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  arg2 = (float *)jarg2; 
  arg3 = (float *)jarg3; 
  arg4 = (float *)jarg4; 
  ((FDatasmithFacadeMaterialsUtils::FUVEditParameters const *)arg1)->GetRotationPivot(*arg2,*arg3,*arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_SetRotationAngle(void * jarg1, float jarg2) {
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetRotationAngle(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_GetRotationAngle(void * jarg1) {
  float jresult ;
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  result = (float)((FDatasmithFacadeMaterialsUtils::FUVEditParameters const *)arg1)->GetRotationAngle();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_SetChannelIndex(void * jarg1, unsigned char jarg2) {
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  uint8 arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  arg2 = (uint8)jarg2; 
  (arg1)->SetChannelIndex(arg2);
}


SWIGEXPORT unsigned char SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_GetChannelIndex(void * jarg1) {
  unsigned char jresult ;
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  uint8 result;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  result = (uint8)((FDatasmithFacadeMaterialsUtils::FUVEditParameters const *)arg1)->GetChannelIndex();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_SetIsUsingRealWorldScale(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetIsUsingRealWorldScale(arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_GetIsUsingRealWorldScale(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  result = (bool)((FDatasmithFacadeMaterialsUtils::FUVEditParameters const *)arg1)->GetIsUsingRealWorldScale();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_SetMirrorU(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetMirrorU(arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_GetMirrorU(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  result = (bool)((FDatasmithFacadeMaterialsUtils::FUVEditParameters const *)arg1)->GetMirrorU();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_SetMirrorV(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetMirrorV(arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FUVEditParameters_GetMirrorV(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  result = (bool)((FDatasmithFacadeMaterialsUtils::FUVEditParameters const *)arg1)->GetMirrorV();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeMaterialsUtils_FUVEditParameters() {
  void * jresult ;
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *result = 0 ;
  
  result = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)new FDatasmithFacadeMaterialsUtils::FUVEditParameters();
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialsUtils_FUVEditParameters(void * jarg1) {
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_CreateTextureExpression(void * jarg1, wchar_t * jarg2, wchar_t * jarg3, void * jarg4) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  TCHAR *arg3 = (TCHAR *) 0 ;
  FDatasmithFacadeMaterialsUtils::FUVEditParameters *arg4 = 0 ;
  FDatasmithFacadeMaterialExpressionTexture *result = 0 ;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1;
  if (!arg1) {
    SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentNullException, "FDatasmithFacadeUEPbrMaterial & type is null", 0);
    return 0;
  } 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (TCHAR *)jarg3; 
  arg4 = (FDatasmithFacadeMaterialsUtils::FUVEditParameters *)jarg4;
  if (!arg4) {
    SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentNullException, "FDatasmithFacadeMaterialsUtils::FUVEditParameters const & type is null", 0);
    return 0;
  } 
  result = (FDatasmithFacadeMaterialExpressionTexture *)FDatasmithFacadeMaterialsUtils::CreateNewFacadeTextureExpression(*arg1,(wchar_t const *)arg2,(wchar_t const *)arg3,(FDatasmithFacadeMaterialsUtils::FUVEditParameters const &)*arg4);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeMaterialsUtils_FWeightedMaterialExpressionParameters(float jarg1) {
  void * jresult ;
  float arg1 ;
  FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *result = 0 ;
  
  arg1 = (float)jarg1; 
  result = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *)new FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters(arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FWeightedMaterialExpressionParameters_SetColor(void * jarg1, float jarg2, float jarg3, float jarg4, float jarg5) {
  FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *) 0 ;
  float arg2 ;
  float arg3 ;
  float arg4 ;
  float arg5 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *)jarg1; 
  arg2 = (float)jarg2; 
  arg3 = (float)jarg3; 
  arg4 = (float)jarg4; 
  arg5 = (float)jarg5; 
  (arg1)->SetColor(arg2,arg3,arg4,arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FWeightedMaterialExpressionParameters_SetColorsRGB(void * jarg1, unsigned char jarg2, unsigned char jarg3, unsigned char jarg4, unsigned char jarg5) {
  FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *) 0 ;
  uint8 arg2 ;
  uint8 arg3 ;
  uint8 arg4 ;
  uint8 arg5 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *)jarg1; 
  arg2 = (uint8)jarg2; 
  arg3 = (uint8)jarg3; 
  arg4 = (uint8)jarg4; 
  arg5 = (uint8)jarg5; 
  (arg1)->SetColorsRGB(arg2,arg3,arg4,arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FWeightedMaterialExpressionParameters_SetScalar(void * jarg1, float jarg2) {
  FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetScalar(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FWeightedMaterialExpressionParameters_SetTextureMode(void * jarg1, int jarg2) {
  FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *) 0 ;
  FDatasmithFacadeTexture::ETextureMode arg2 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *)jarg1; 
  arg2 = (FDatasmithFacadeTexture::ETextureMode)jarg2; 
  (arg1)->SetTextureMode(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_FWeightedMaterialExpressionParameters_SetExpression(void * jarg1, void * jarg2) {
  FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *) 0 ;
  FDatasmithFacadeMaterialExpression *arg2 = 0 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *)jarg1; 
  arg2 = (FDatasmithFacadeMaterialExpression *)jarg2;
  if (!arg2) {
    SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentNullException, "FDatasmithFacadeMaterialExpression const & type is null", 0);
    return ;
  } 
  (arg1)->SetExpression((FDatasmithFacadeMaterialExpression const &)*arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialsUtils_FWeightedMaterialExpressionParameters(void * jarg1) {
  FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *arg1 = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_CreateWeightedMaterialExpression(void * jarg1, wchar_t * jarg2, void * jarg3) {
  void * jresult ;
  FDatasmithFacadeUEPbrMaterial *arg1 = 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *arg3 = 0 ;
  FDatasmithFacadeMaterialExpression *result = 0 ;
  
  arg1 = (FDatasmithFacadeUEPbrMaterial *)jarg1;
  if (!arg1) {
    SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentNullException, "FDatasmithFacadeUEPbrMaterial & type is null", 0);
    return 0;
  } 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters *)jarg3;
  if (!arg3) {
    SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentNullException, "FDatasmithFacadeMaterialsUtils::FWeightedMaterialExpressionParameters & type is null", 0);
    return 0;
  } 
  result = (FDatasmithFacadeMaterialExpression *)FDatasmithFacadeMaterialsUtils::CreateNewFacadeWeightedMaterialExpression(*arg1,(wchar_t const *)arg2,*arg3);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_CreateSimpleTextureElement__SWIG_0(wchar_t * jarg1, int jarg2) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeTexture::ETextureMode arg2 ;
  FDatasmithFacadeTexture *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  arg2 = (FDatasmithFacadeTexture::ETextureMode)jarg2; 
  result = (FDatasmithFacadeTexture *)FDatasmithFacadeMaterialsUtils::CreateSimpleTextureElement((wchar_t const *)arg1,arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialsUtils_CreateSimpleTextureElement__SWIG_1(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeTexture *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeTexture *)FDatasmithFacadeMaterialsUtils::CreateSimpleTextureElement((wchar_t const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMaterialsUtils(void * jarg1) {
  FDatasmithFacadeMaterialsUtils *arg1 = (FDatasmithFacadeMaterialsUtils *) 0 ;
  
  arg1 = (FDatasmithFacadeMaterialsUtils *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeMesh() {
  void * jresult ;
  FDatasmithFacadeMesh *result = 0 ;
  
  result = (FDatasmithFacadeMesh *)new FDatasmithFacadeMesh();
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetName((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeMesh const *)arg1)->GetName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_InternalCalculateHash(void * jarg1, void * jarg2, unsigned long jarg3) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  TCHAR *arg2 ;
  size_t arg3 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (size_t)jarg3; 
  ((FDatasmithFacadeMesh const *)arg1)->CalculateHash(arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetFacesCount(void * jarg1, int jarg2) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->SetFacesCount(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetFacesCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  result = (int32)((FDatasmithFacadeMesh const *)arg1)->GetFacesCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetFace__SWIG_0(void * jarg1, int jarg2, int jarg3, int jarg4, int jarg5, int jarg6) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  int32 arg3 ;
  int32 arg4 ;
  int32 arg5 ;
  int32 arg6 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (int32)jarg3; 
  arg4 = (int32)jarg4; 
  arg5 = (int32)jarg5; 
  arg6 = (int32)jarg6; 
  (arg1)->SetFace(arg2,arg3,arg4,arg5,arg6);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetFace__SWIG_1(void * jarg1, int jarg2, int jarg3, int jarg4, int jarg5) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  int32 arg3 ;
  int32 arg4 ;
  int32 arg5 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (int32)jarg3; 
  arg4 = (int32)jarg4; 
  arg5 = (int32)jarg5; 
  (arg1)->SetFace(arg2,arg3,arg4,arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetFace(void * jarg1, int jarg2, int * jarg3, int * jarg4, int * jarg5, int * jarg6) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  int32 *arg3 = 0 ;
  int32 *arg4 = 0 ;
  int32 *arg5 = 0 ;
  int32 *arg6 = 0 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (int32 *)jarg3; 
  arg4 = (int32 *)jarg4; 
  arg5 = (int32 *)jarg5; 
  arg6 = (int32 *)jarg6; 
  ((FDatasmithFacadeMesh const *)arg1)->GetFace(arg2,*arg3,*arg4,*arg5,*arg6);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetFaceSmoothingMask(void * jarg1, int jarg2, unsigned int jarg3) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  uint32 arg3 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (uint32)jarg3; 
  (arg1)->SetFaceSmoothingMask(arg2,arg3);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetFaceSmoothingMask(void * jarg1, int jarg2) {
  unsigned int jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  uint32 result;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (uint32)((FDatasmithFacadeMesh const *)arg1)->GetFaceSmoothingMask(arg2);
  jresult = result; 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetMaterialsCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  result = (int32)((FDatasmithFacadeMesh const *)arg1)->GetMaterialsCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeMesh_IsMaterialIdUsed(void * jarg1, int jarg2) {
  unsigned int jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  bool result;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (bool)((FDatasmithFacadeMesh const *)arg1)->IsMaterialIdUsed(arg2);
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetVerticesCount(void * jarg1, int jarg2) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->SetVerticesCount(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetVerticesCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  result = (int32)((FDatasmithFacadeMesh const *)arg1)->GetVerticesCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetVertex(void * jarg1, int jarg2, float jarg3, float jarg4, float jarg5) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  float arg3 ;
  float arg4 ;
  float arg5 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (float)jarg3; 
  arg4 = (float)jarg4; 
  arg5 = (float)jarg5; 
  (arg1)->SetVertex(arg2,arg3,arg4,arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetVertex(void * jarg1, int jarg2, float * jarg3, float * jarg4, float * jarg5) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  float *arg3 = 0 ;
  float *arg4 = 0 ;
  float *arg5 = 0 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (float *)jarg3; 
  arg4 = (float *)jarg4; 
  arg5 = (float *)jarg5; 
  ((FDatasmithFacadeMesh const *)arg1)->GetVertex(arg2,*arg3,*arg4,*arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetNormal(void * jarg1, int jarg2, float jarg3, float jarg4, float jarg5) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  float arg3 ;
  float arg4 ;
  float arg5 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (float)jarg3; 
  arg4 = (float)jarg4; 
  arg5 = (float)jarg5; 
  (arg1)->SetNormal(arg2,arg3,arg4,arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetNormal(void * jarg1, int jarg2, float * jarg3, float * jarg4, float * jarg5) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  float *arg3 = 0 ;
  float *arg4 = 0 ;
  float *arg5 = 0 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (float *)jarg3; 
  arg4 = (float *)jarg4; 
  arg5 = (float *)jarg5; 
  ((FDatasmithFacadeMesh const *)arg1)->GetNormal(arg2,*arg3,*arg4,*arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetUVChannelsCount(void * jarg1, int jarg2) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->SetUVChannelsCount(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_AddUVChannel(void * jarg1) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  (arg1)->AddUVChannel();
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_RemoveUVChannel(void * jarg1) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  (arg1)->RemoveUVChannel();
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetUVChannelsCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  result = (int32)((FDatasmithFacadeMesh const *)arg1)->GetUVChannelsCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetUVCount(void * jarg1, int jarg2, int jarg3) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  int32 arg3 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (int32)jarg3; 
  (arg1)->SetUVCount(arg2,arg3);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetUVCount(void * jarg1, int jarg2) {
  int jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (int32)((FDatasmithFacadeMesh const *)arg1)->GetUVCount(arg2);
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetUV(void * jarg1, int jarg2, int jarg3, double jarg4, double jarg5) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  int32 arg3 ;
  double arg4 ;
  double arg5 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (int32)jarg3; 
  arg4 = (double)jarg4; 
  arg5 = (double)jarg5; 
  (arg1)->SetUV(arg2,arg3,arg4,arg5);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetHashForUVChannel(void * jarg1, int jarg2) {
  unsigned int jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  uint32 result;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (uint32)((FDatasmithFacadeMesh const *)arg1)->GetHashForUVChannel(arg2);
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetUV(void * jarg1, int jarg2, int jarg3, double * jarg4, double * jarg5) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  int32 arg3 ;
  double *arg4 = 0 ;
  double *arg5 = 0 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (int32)jarg3; 
  arg4 = (double *)jarg4; 
  arg5 = (double *)jarg5; 
  ((FDatasmithFacadeMesh const *)arg1)->GetUV(arg2,arg3,*arg4,*arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetFaceUV(void * jarg1, int jarg2, int jarg3, int jarg4, int jarg5, int jarg6) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  int32 arg3 ;
  int32 arg4 ;
  int32 arg5 ;
  int32 arg6 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (int32)jarg3; 
  arg4 = (int32)jarg4; 
  arg5 = (int32)jarg5; 
  arg6 = (int32)jarg6; 
  (arg1)->SetFaceUV(arg2,arg3,arg4,arg5,arg6);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetFaceUV(void * jarg1, int jarg2, int jarg3, int * jarg4, int * jarg5, int * jarg6) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  int32 arg3 ;
  int32 *arg4 = 0 ;
  int32 *arg5 = 0 ;
  int32 *arg6 = 0 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (int32)jarg3; 
  arg4 = (int32 *)jarg4; 
  arg5 = (int32 *)jarg5; 
  arg6 = (int32 *)jarg6; 
  ((FDatasmithFacadeMesh const *)arg1)->GetFaceUV(arg2,arg3,*arg4,*arg5,*arg6);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetVertexColorCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  result = (int32)((FDatasmithFacadeMesh const *)arg1)->GetVertexColorCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetVertexColor(void * jarg1, int jarg2, unsigned char jarg3, unsigned char jarg4, unsigned char jarg5, unsigned char jarg6) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  uint8 arg3 ;
  uint8 arg4 ;
  uint8 arg5 ;
  uint8 arg6 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (uint8)jarg3; 
  arg4 = (uint8)jarg4; 
  arg5 = (uint8)jarg5; 
  arg6 = (uint8)jarg6; 
  (arg1)->SetVertexColor(arg2,arg3,arg4,arg5,arg6);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetVertexColor(void * jarg1, int jarg2, unsigned char * jarg3, unsigned char jarg4, unsigned char * jarg5, unsigned char * jarg6) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  uint8 *arg3 = 0 ;
  uint8 arg4 ;
  uint8 *arg5 = 0 ;
  uint8 *arg6 = 0 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (uint8 *)jarg3; 
  arg4 = (uint8)jarg4; 
  arg5 = (uint8 *)jarg5; 
  arg6 = (uint8 *)jarg6; 
  ((FDatasmithFacadeMesh const *)arg1)->GetVertexColor(arg2,*arg3,arg4,*arg5,*arg6);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_SetLightmapSourceUVChannel(void * jarg1, int jarg2) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->SetLightmapSourceUVChannel(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetLightmapSourceUVChannel(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  result = (int32)((FDatasmithFacadeMesh const *)arg1)->GetLightmapSourceUVChannel();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMesh_AddLOD(void * jarg1, void * jarg2) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  FDatasmithFacadeMesh *arg2 = 0 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (FDatasmithFacadeMesh *)jarg2;
  if (!arg2) {
    SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentNullException, "FDatasmithFacadeMesh const & type is null", 0);
    return ;
  } 
  (arg1)->AddLOD((FDatasmithFacadeMesh const &)*arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetLODsCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  result = (int32)((FDatasmithFacadeMesh const *)arg1)->GetLODsCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMesh_GetLOD(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeMesh *result = 0 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeMesh *)(arg1)->GetNewLOD(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeMesh_ComputeArea(void * jarg1) {
  float jresult ;
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  result = (float)((FDatasmithFacadeMesh const *)arg1)->ComputeArea();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMesh(void * jarg1) {
  FDatasmithFacadeMesh *arg1 = (FDatasmithFacadeMesh *) 0 ;
  
  arg1 = (FDatasmithFacadeMesh *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeMeshElement(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeMeshElement *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeMeshElement *)new FDatasmithFacadeMeshElement((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_GetFile(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeMeshElement const *)arg1)->GetFile();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_SetFile(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetFile((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_InternalGetFileHash(void * jarg1, void * jarg2, unsigned long jarg3) {
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  TCHAR *arg2 ;
  size_t arg3 ;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (size_t)jarg3; 
  ((FDatasmithFacadeMeshElement const *)arg1)->GetFileHash(arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_SetFileHash(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetFileHash((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_SetDimensions(void * jarg1, float jarg2, float jarg3, float jarg4, float jarg5) {
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  float arg2 ;
  float arg3 ;
  float arg4 ;
  float arg5 ;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  arg2 = (float)jarg2; 
  arg3 = (float)jarg3; 
  arg4 = (float)jarg4; 
  arg5 = (float)jarg5; 
  (arg1)->SetDimensions(arg2,arg3,arg4,arg5);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_GetArea(void * jarg1) {
  float jresult ;
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  result = (float)((FDatasmithFacadeMeshElement const *)arg1)->GetArea();
  jresult = result; 
  return jresult;
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_GetWidth(void * jarg1) {
  float jresult ;
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  result = (float)((FDatasmithFacadeMeshElement const *)arg1)->GetWidth();
  jresult = result; 
  return jresult;
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_GetHeight(void * jarg1) {
  float jresult ;
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  result = (float)((FDatasmithFacadeMeshElement const *)arg1)->GetHeight();
  jresult = result; 
  return jresult;
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_GetDepth(void * jarg1) {
  float jresult ;
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  result = (float)((FDatasmithFacadeMeshElement const *)arg1)->GetDepth();
  jresult = result; 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_GetLightmapCoordinateIndex(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  result = (int32)((FDatasmithFacadeMeshElement const *)arg1)->GetLightmapCoordinateIndex();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_SetLightmapCoordinateIndex(void * jarg1, int jarg2) {
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->SetLightmapCoordinateIndex(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_GetLightmapSourceUV(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  result = (int32)((FDatasmithFacadeMeshElement const *)arg1)->GetLightmapSourceUV();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_SetLightmapSourceUV(void * jarg1, int jarg2) {
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->SetLightmapSourceUV(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_SetMaterial(void * jarg1, wchar_t * jarg2, int jarg3) {
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  int32 arg3 ;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (int32)jarg3; 
  (arg1)->SetMaterial((TCHAR const *)arg2,arg3);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_GetMaterial(void * jarg1, int jarg2) {
  wchar_t * jresult ;
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  int32 arg2 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (TCHAR *)((FDatasmithFacadeMeshElement const *)arg1)->GetMaterial(arg2);
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_GetMaterialSlotCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  result = (int32)((FDatasmithFacadeMeshElement const *)arg1)->GetMaterialSlotCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_GetMaterialSlotAt(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeMaterialID *result = 0 ;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeMaterialID *)(arg1)->GetMaterialSlotAt(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMeshElement(void * jarg1) {
  FDatasmithFacadeMeshElement *arg1 = (FDatasmithFacadeMeshElement *) 0 ;
  
  arg1 = (FDatasmithFacadeMeshElement *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeMetaData(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeMetaData *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeMetaData *)new FDatasmithFacadeMetaData((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMetaData_AddPropertyBoolean(void * jarg1, wchar_t * jarg2, unsigned int jarg3) {
  FDatasmithFacadeMetaData *arg1 = (FDatasmithFacadeMetaData *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  bool arg3 ;
  
  arg1 = (FDatasmithFacadeMetaData *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = jarg3 ? true : false; 
  (arg1)->AddPropertyBoolean((TCHAR const *)arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMetaData_AddPropertyColor(void * jarg1, wchar_t * jarg2, unsigned char jarg3, unsigned char jarg4, unsigned char jarg5, unsigned char jarg6) {
  FDatasmithFacadeMetaData *arg1 = (FDatasmithFacadeMetaData *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  uint8 arg3 ;
  uint8 arg4 ;
  uint8 arg5 ;
  uint8 arg6 ;
  
  arg1 = (FDatasmithFacadeMetaData *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (uint8)jarg3; 
  arg4 = (uint8)jarg4; 
  arg5 = (uint8)jarg5; 
  arg6 = (uint8)jarg6; 
  (arg1)->AddPropertyColor((TCHAR const *)arg2,arg3,arg4,arg5,arg6);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMetaData_AddPropertyFloat(void * jarg1, wchar_t * jarg2, float jarg3) {
  FDatasmithFacadeMetaData *arg1 = (FDatasmithFacadeMetaData *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  float arg3 ;
  
  arg1 = (FDatasmithFacadeMetaData *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (float)jarg3; 
  (arg1)->AddPropertyFloat((TCHAR const *)arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMetaData_AddPropertyString(void * jarg1, wchar_t * jarg2, wchar_t * jarg3) {
  FDatasmithFacadeMetaData *arg1 = (FDatasmithFacadeMetaData *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  TCHAR *arg3 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMetaData *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (TCHAR *)jarg3; 
  (arg1)->AddPropertyString((TCHAR const *)arg2,(TCHAR const *)arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMetaData_AddPropertyTexture(void * jarg1, wchar_t * jarg2, wchar_t * jarg3) {
  FDatasmithFacadeMetaData *arg1 = (FDatasmithFacadeMetaData *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  TCHAR *arg3 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMetaData *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (TCHAR *)jarg3; 
  (arg1)->AddPropertyTexture((TCHAR const *)arg2,(TCHAR const *)arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMetaData_AddPropertyVector(void * jarg1, wchar_t * jarg2, wchar_t * jarg3) {
  FDatasmithFacadeMetaData *arg1 = (FDatasmithFacadeMetaData *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  TCHAR *arg3 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeMetaData *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (TCHAR *)jarg3; 
  (arg1)->AddPropertyVector((TCHAR const *)arg2,(TCHAR const *)arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMetaData_AddProperty(void * jarg1, void * jarg2) {
  FDatasmithFacadeMetaData *arg1 = (FDatasmithFacadeMetaData *) 0 ;
  FDatasmithFacadeKeyValueProperty *arg2 = (FDatasmithFacadeKeyValueProperty *) 0 ;
  
  arg1 = (FDatasmithFacadeMetaData *)jarg1; 
  arg2 = (FDatasmithFacadeKeyValueProperty *)jarg2; 
  (arg1)->AddProperty((FDatasmithFacadeKeyValueProperty const *)arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeMetaData_GetPropertiesCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeMetaData *arg1 = (FDatasmithFacadeMetaData *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeMetaData *)jarg1; 
  result = (int32)((FDatasmithFacadeMetaData const *)arg1)->GetPropertiesCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeMetaData_GetProperty(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeMetaData *arg1 = (FDatasmithFacadeMetaData *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeKeyValueProperty *result = 0 ;
  
  arg1 = (FDatasmithFacadeMetaData *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeKeyValueProperty *)((FDatasmithFacadeMetaData const *)arg1)->GetNewProperty(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMetaData_SetAssociatedElement(void * jarg1, void * jarg2) {
  FDatasmithFacadeMetaData *arg1 = (FDatasmithFacadeMetaData *) 0 ;
  FDatasmithFacadeElement *arg2 = (FDatasmithFacadeElement *) 0 ;
  
  arg1 = (FDatasmithFacadeMetaData *)jarg1; 
  arg2 = (FDatasmithFacadeElement *)jarg2; 
  (arg1)->SetAssociatedElement((FDatasmithFacadeElement const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMetaData_RemoveProperty(void * jarg1, void * jarg2) {
  FDatasmithFacadeMetaData *arg1 = (FDatasmithFacadeMetaData *) 0 ;
  FDatasmithFacadeKeyValueProperty *arg2 = (FDatasmithFacadeKeyValueProperty *) 0 ;
  
  arg1 = (FDatasmithFacadeMetaData *)jarg1; 
  arg2 = (FDatasmithFacadeKeyValueProperty *)jarg2; 
  (arg1)->RemoveProperty((FDatasmithFacadeKeyValueProperty const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeMetaData_ResetProperties(void * jarg1) {
  FDatasmithFacadeMetaData *arg1 = (FDatasmithFacadeMetaData *) 0 ;
  
  arg1 = (FDatasmithFacadeMetaData *)jarg1; 
  (arg1)->ResetProperties();
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeMetaData(void * jarg1) {
  FDatasmithFacadeMetaData *arg1 = (FDatasmithFacadeMetaData *) 0 ;
  
  arg1 = (FDatasmithFacadeMetaData *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeActor(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeActor *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeActor *)new FDatasmithFacadeActor((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeActor(void * jarg1) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_SetWorldTransform__SWIG_0(void * jarg1, double * jarg2, unsigned int jarg3) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  double *arg2 ;
  bool arg3 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (double *)jarg2; 
  arg3 = jarg3 ? true : false; 
  (arg1)->SetWorldTransform((double const (*))arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_SetWorldTransform__SWIG_1(void * jarg1, double * jarg2) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  double *arg2 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (double *)jarg2; 
  (arg1)->SetWorldTransform((double const (*))arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_SetWorldTransform__SWIG_2(void * jarg1, float * jarg2, unsigned int jarg3) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  float *arg2 ;
  bool arg3 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (float *)jarg2; 
  arg3 = jarg3 ? true : false; 
  (arg1)->SetWorldTransform((float const (*))arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_SetWorldTransform__SWIG_3(void * jarg1, float * jarg2) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  float *arg2 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (float *)jarg2; 
  (arg1)->SetWorldTransform((float const (*))arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_SetScale(void * jarg1, double jarg2, double jarg3, double jarg4) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (double)jarg2; 
  arg3 = (double)jarg3; 
  arg4 = (double)jarg4; 
  (arg1)->SetScale(arg2,arg3,arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_GetScale(void * jarg1, double * jarg2, double * jarg3, double * jarg4) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  double *arg2 = 0 ;
  double *arg3 = 0 ;
  double *arg4 = 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (double *)jarg2; 
  arg3 = (double *)jarg3; 
  arg4 = (double *)jarg4; 
  ((FDatasmithFacadeActor const *)arg1)->GetScale(*arg2,*arg3,*arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_SetRotation__SWIG_0(void * jarg1, double jarg2, double jarg3, double jarg4) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (double)jarg2; 
  arg3 = (double)jarg3; 
  arg4 = (double)jarg4; 
  (arg1)->SetRotation(arg2,arg3,arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_GetRotation__SWIG_0(void * jarg1, double * jarg2, double * jarg3, double * jarg4) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  double *arg2 = 0 ;
  double *arg3 = 0 ;
  double *arg4 = 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (double *)jarg2; 
  arg3 = (double *)jarg3; 
  arg4 = (double *)jarg4; 
  ((FDatasmithFacadeActor const *)arg1)->GetRotation(*arg2,*arg3,*arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_SetRotation__SWIG_1(void * jarg1, double jarg2, double jarg3, double jarg4, double jarg5) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  double arg5 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (double)jarg2; 
  arg3 = (double)jarg3; 
  arg4 = (double)jarg4; 
  arg5 = (double)jarg5; 
  (arg1)->SetRotation(arg2,arg3,arg4,arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_GetRotation__SWIG_1(void * jarg1, double * jarg2, double * jarg3, double * jarg4, double * jarg5) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  double *arg2 = 0 ;
  double *arg3 = 0 ;
  double *arg4 = 0 ;
  double *arg5 = 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (double *)jarg2; 
  arg3 = (double *)jarg3; 
  arg4 = (double *)jarg4; 
  arg5 = (double *)jarg5; 
  ((FDatasmithFacadeActor const *)arg1)->GetRotation(*arg2,*arg3,*arg4,*arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_SetTranslation(void * jarg1, double jarg2, double jarg3, double jarg4) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (double)jarg2; 
  arg3 = (double)jarg3; 
  arg4 = (double)jarg4; 
  (arg1)->SetTranslation(arg2,arg3,arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_GetTranslation(void * jarg1, double * jarg2, double * jarg3, double * jarg4) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  double *arg2 = 0 ;
  double *arg3 = 0 ;
  double *arg4 = 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (double *)jarg2; 
  arg3 = (double *)jarg3; 
  arg4 = (double *)jarg4; 
  ((FDatasmithFacadeActor const *)arg1)->GetTranslation(*arg2,*arg3,*arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_SetLayer(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetLayer((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeActor_GetLayer(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeActor const *)arg1)->GetLayer();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_AddTag(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->AddTag((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_ResetTags(void * jarg1) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  (arg1)->ResetTags();
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeActor_GetTagsCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  result = (int32)((FDatasmithFacadeActor const *)arg1)->GetTagsCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeActor_GetTag(void * jarg1, int jarg2) {
  wchar_t * jresult ;
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  int32 arg2 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (TCHAR *)((FDatasmithFacadeActor const *)arg1)->GetTag(arg2);
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeActor_IsComponent(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  result = (bool)((FDatasmithFacadeActor const *)arg1)->IsComponent();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_SetIsComponent(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetIsComponent(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_AddChild(void * jarg1, void * jarg2) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  FDatasmithFacadeActor *arg2 = (FDatasmithFacadeActor *) 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (FDatasmithFacadeActor *)jarg2; 
  (arg1)->AddChild(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeActor_GetChildrenCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  result = (int32)((FDatasmithFacadeActor const *)arg1)->GetChildrenCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeActor_GetChild(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeActor *result = 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeActor *)(arg1)->GetNewChild(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_RemoveChild(void * jarg1, void * jarg2) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  FDatasmithFacadeActor *arg2 = (FDatasmithFacadeActor *) 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = (FDatasmithFacadeActor *)jarg2; 
  (arg1)->RemoveChild(arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeActor_GetParentActor(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  FDatasmithFacadeActor *result = 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  result = (FDatasmithFacadeActor *)((FDatasmithFacadeActor const *)arg1)->GetNewParentActor();
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_SetVisibility(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetVisibility(arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeActor_GetVisibility(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  result = (bool)((FDatasmithFacadeActor const *)arg1)->GetVisibility();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActor_SetCastShadow(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetCastShadow(arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeActor_GetCastShadow(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  result = (bool)((FDatasmithFacadeActor const *)arg1)->GetCastShadow();
  jresult = result; 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeActor_GetActorType(void * jarg1) {
  int jresult ;
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  FDatasmithFacadeActor::EActorType result;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  result = (FDatasmithFacadeActor::EActorType)((FDatasmithFacadeActor const *)arg1)->GetActorType();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeActorCamera(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeActorCamera *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeActorCamera *)new FDatasmithFacadeActorCamera((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeActorCamera(void * jarg1) {
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_SetCameraPosition(void * jarg1, double jarg2, double jarg3, double jarg4) {
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  arg2 = (double)jarg2; 
  arg3 = (double)jarg3; 
  arg4 = (double)jarg4; 
  (arg1)->SetCameraPosition(arg2,arg3,arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_SetCameraRotation(void * jarg1, double jarg2, double jarg3, double jarg4, double jarg5, double jarg6, double jarg7) {
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  double arg5 ;
  double arg6 ;
  double arg7 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  arg2 = (double)jarg2; 
  arg3 = (double)jarg3; 
  arg4 = (double)jarg4; 
  arg5 = (double)jarg5; 
  arg6 = (double)jarg6; 
  arg7 = (double)jarg7; 
  (arg1)->SetCameraRotation(arg2,arg3,arg4,arg5,arg6,arg7);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_SetSensorWidth(void * jarg1, float jarg2) {
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetSensorWidth(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_GetSensorWidth(void * jarg1) {
  float jresult ;
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  result = (float)((FDatasmithFacadeActorCamera const *)arg1)->GetSensorWidth();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_SetAspectRatio(void * jarg1, float jarg2) {
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetAspectRatio(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_GetAspectRatio(void * jarg1) {
  float jresult ;
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  result = (float)((FDatasmithFacadeActorCamera const *)arg1)->GetAspectRatio();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_SetFocusDistance__SWIG_0(void * jarg1, float jarg2, float jarg3, float jarg4) {
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  float arg2 ;
  float arg3 ;
  float arg4 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  arg2 = (float)jarg2; 
  arg3 = (float)jarg3; 
  arg4 = (float)jarg4; 
  (arg1)->SetFocusDistance(arg2,arg3,arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_SetFocusDistance__SWIG_1(void * jarg1, float jarg2) {
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetFocusDistance(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_GetFocusDistance(void * jarg1) {
  float jresult ;
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  result = (float)((FDatasmithFacadeActorCamera const *)arg1)->GetFocusDistance();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_SetFocalLength__SWIG_0(void * jarg1, float jarg2) {
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetFocalLength(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_SetFocalLength__SWIG_1(void * jarg1, float jarg2, unsigned int jarg3) {
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  float arg2 ;
  bool arg3 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  arg2 = (float)jarg2; 
  arg3 = jarg3 ? true : false; 
  (arg1)->SetFocalLength(arg2,arg3);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_GetFocalLength(void * jarg1) {
  float jresult ;
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  result = (float)((FDatasmithFacadeActorCamera const *)arg1)->GetFocalLength();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_SetLookAtActor(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetLookAtActor((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_GetLookAtActor(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeActorCamera const *)arg1)->GetLookAtActor();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_SetLookAtAllowRoll(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetLookAtAllowRoll(arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_GetLookAtAllowRoll(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  result = (bool)((FDatasmithFacadeActorCamera const *)arg1)->GetLookAtAllowRoll();
  jresult = result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_GetEnableDepthOfField(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  result = (bool)((FDatasmithFacadeActorCamera const *)arg1)->GetEnableDepthOfField();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_SetEnableDepthOfField(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetEnableDepthOfField(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_GetFStop(void * jarg1) {
  float jresult ;
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  result = (float)((FDatasmithFacadeActorCamera const *)arg1)->GetFStop();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_SetFStop(void * jarg1, float jarg2) {
  FDatasmithFacadeActorCamera *arg1 = (FDatasmithFacadeActorCamera *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeActorCamera *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetFStop(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeActorLight(void * jarg1) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  delete arg1;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_IsEnabled(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  result = (bool)((FDatasmithFacadeActorLight const *)arg1)->IsEnabled();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetEnabled(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetEnabled(arg2);
}


SWIGEXPORT double SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_GetIntensity(void * jarg1) {
  double jresult ;
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  double result;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  result = (double)((FDatasmithFacadeActorLight const *)arg1)->GetIntensity();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetIntensity(void * jarg1, double jarg2) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  double arg2 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (double)jarg2; 
  (arg1)->SetIntensity(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_GetColor__SWIG_0(void * jarg1, unsigned char * jarg2, unsigned char * jarg3, unsigned char * jarg4, unsigned char * jarg5) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  uint8 *arg2 = 0 ;
  uint8 *arg3 = 0 ;
  uint8 *arg4 = 0 ;
  uint8 *arg5 = 0 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (uint8 *)jarg2; 
  arg3 = (uint8 *)jarg3; 
  arg4 = (uint8 *)jarg4; 
  arg5 = (uint8 *)jarg5; 
  ((FDatasmithFacadeActorLight const *)arg1)->GetColor(*arg2,*arg3,*arg4,*arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_GetColor__SWIG_1(void * jarg1, float * jarg2, float * jarg3, float * jarg4, float * jarg5) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  float *arg2 = 0 ;
  float *arg3 = 0 ;
  float *arg4 = 0 ;
  float *arg5 = 0 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (float *)jarg2; 
  arg3 = (float *)jarg3; 
  arg4 = (float *)jarg4; 
  arg5 = (float *)jarg5; 
  ((FDatasmithFacadeActorLight const *)arg1)->GetColor(*arg2,*arg3,*arg4,*arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetColor__SWIG_0(void * jarg1, unsigned char jarg2, unsigned char jarg3, unsigned char jarg4, unsigned char jarg5) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  uint8 arg2 ;
  uint8 arg3 ;
  uint8 arg4 ;
  uint8 arg5 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (uint8)jarg2; 
  arg3 = (uint8)jarg3; 
  arg4 = (uint8)jarg4; 
  arg5 = (uint8)jarg5; 
  (arg1)->SetColor(arg2,arg3,arg4,arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetColor__SWIG_1(void * jarg1, float jarg2, float jarg3, float jarg4, float jarg5) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  float arg2 ;
  float arg3 ;
  float arg4 ;
  float arg5 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (float)jarg2; 
  arg3 = (float)jarg3; 
  arg4 = (float)jarg4; 
  arg5 = (float)jarg5; 
  (arg1)->SetColor(arg2,arg3,arg4,arg5);
}


SWIGEXPORT double SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_GetTemperature(void * jarg1) {
  double jresult ;
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  double result;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  result = (double)((FDatasmithFacadeActorLight const *)arg1)->GetTemperature();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetTemperature(void * jarg1, double jarg2) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  double arg2 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (double)jarg2; 
  (arg1)->SetTemperature(arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_GetUseTemperature(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  result = (bool)((FDatasmithFacadeActorLight const *)arg1)->GetUseTemperature();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetUseTemperature(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetUseTemperature(arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_GetIesFile(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeActorLight const *)arg1)->GetIesFile();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_WriteIESFile(void * jarg1, wchar_t * jarg2, wchar_t * jarg3, wchar_t * jarg4) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  TCHAR *arg3 = (TCHAR *) 0 ;
  TCHAR *arg4 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (TCHAR *)jarg3; 
  arg4 = (TCHAR *)jarg4; 
  (arg1)->WriteIESFile((TCHAR const *)arg2,(TCHAR const *)arg3,(TCHAR const *)arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetIesFile(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetIesFile((TCHAR const *)arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_GetUseIes(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  result = (bool)((FDatasmithFacadeActorLight const *)arg1)->GetUseIes();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetUseIes(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetUseIes(arg2);
}


SWIGEXPORT double SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_GetIesBrightnessScale(void * jarg1) {
  double jresult ;
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  double result;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  result = (double)((FDatasmithFacadeActorLight const *)arg1)->GetIesBrightnessScale();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetIesBrightnessScale(void * jarg1, double jarg2) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  double arg2 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (double)jarg2; 
  (arg1)->SetIesBrightnessScale(arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_GetUseIesBrightness(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  result = (bool)((FDatasmithFacadeActorLight const *)arg1)->GetUseIesBrightness();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetUseIesBrightness(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetUseIesBrightness(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_GetIesRotation__SWIG_0(void * jarg1, float * jarg2, float * jarg3, float * jarg4, float * jarg5) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  float *arg2 = 0 ;
  float *arg3 = 0 ;
  float *arg4 = 0 ;
  float *arg5 = 0 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (float *)jarg2; 
  arg3 = (float *)jarg3; 
  arg4 = (float *)jarg4; 
  arg5 = (float *)jarg5; 
  ((FDatasmithFacadeActorLight const *)arg1)->GetIesRotation(*arg2,*arg3,*arg4,*arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_GetIesRotation__SWIG_1(void * jarg1, float * jarg2, float * jarg3, float * jarg4) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  float *arg2 = 0 ;
  float *arg3 = 0 ;
  float *arg4 = 0 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (float *)jarg2; 
  arg3 = (float *)jarg3; 
  arg4 = (float *)jarg4; 
  ((FDatasmithFacadeActorLight const *)arg1)->GetIesRotation(*arg2,*arg3,*arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetIesRotation__SWIG_0(void * jarg1, float jarg2, float jarg3, float jarg4, float jarg5) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  float arg2 ;
  float arg3 ;
  float arg4 ;
  float arg5 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (float)jarg2; 
  arg3 = (float)jarg3; 
  arg4 = (float)jarg4; 
  arg5 = (float)jarg5; 
  (arg1)->SetIesRotation(arg2,arg3,arg4,arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetIesRotation__SWIG_1(void * jarg1, float jarg2, float jarg3, float jarg4) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  float arg2 ;
  float arg3 ;
  float arg4 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (float)jarg2; 
  arg3 = (float)jarg3; 
  arg4 = (float)jarg4; 
  (arg1)->SetIesRotation(arg2,arg3,arg4);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_GetLightFunctionMaterial(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  FDatasmithFacadeMaterialID *result = 0 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  result = (FDatasmithFacadeMaterialID *)(arg1)->GetNewLightFunctionMaterial();
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetLightFunctionMaterial__SWIG_0(void * jarg1, void * jarg2) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  FDatasmithFacadeMaterialID *arg2 = 0 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (FDatasmithFacadeMaterialID *)jarg2;
  if (!arg2) {
    SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentNullException, "FDatasmithFacadeMaterialID & type is null", 0);
    return ;
  } 
  (arg1)->SetLightFunctionMaterial(*arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SetLightFunctionMaterial__SWIG_1(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeActorLight *arg1 = (FDatasmithFacadeActorLight *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeActorLight *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetLightFunctionMaterial((TCHAR const *)arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadePointLight(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadePointLight *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadePointLight *)new FDatasmithFacadePointLight((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadePointLight(void * jarg1) {
  FDatasmithFacadePointLight *arg1 = (FDatasmithFacadePointLight *) 0 ;
  
  arg1 = (FDatasmithFacadePointLight *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadePointLight_SetIntensityUnits(void * jarg1, int jarg2) {
  FDatasmithFacadePointLight *arg1 = (FDatasmithFacadePointLight *) 0 ;
  FDatasmithFacadePointLight::EPointLightIntensityUnit arg2 ;
  
  arg1 = (FDatasmithFacadePointLight *)jarg1; 
  arg2 = (FDatasmithFacadePointLight::EPointLightIntensityUnit)jarg2; 
  (arg1)->SetIntensityUnits(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadePointLight_GetIntensityUnits(void * jarg1) {
  int jresult ;
  FDatasmithFacadePointLight *arg1 = (FDatasmithFacadePointLight *) 0 ;
  FDatasmithFacadePointLight::EPointLightIntensityUnit result;
  
  arg1 = (FDatasmithFacadePointLight *)jarg1; 
  result = (FDatasmithFacadePointLight::EPointLightIntensityUnit)((FDatasmithFacadePointLight const *)arg1)->GetIntensityUnits();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadePointLight_GetSourceRadius(void * jarg1) {
  float jresult ;
  FDatasmithFacadePointLight *arg1 = (FDatasmithFacadePointLight *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadePointLight *)jarg1; 
  result = (float)((FDatasmithFacadePointLight const *)arg1)->GetSourceRadius();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadePointLight_SetSourceRadius(void * jarg1, float jarg2) {
  FDatasmithFacadePointLight *arg1 = (FDatasmithFacadePointLight *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadePointLight *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetSourceRadius(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadePointLight_GetSourceLength(void * jarg1) {
  float jresult ;
  FDatasmithFacadePointLight *arg1 = (FDatasmithFacadePointLight *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadePointLight *)jarg1; 
  result = (float)((FDatasmithFacadePointLight const *)arg1)->GetSourceLength();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadePointLight_SetSourceLength(void * jarg1, float jarg2) {
  FDatasmithFacadePointLight *arg1 = (FDatasmithFacadePointLight *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadePointLight *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetSourceLength(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadePointLight_GetAttenuationRadius(void * jarg1) {
  float jresult ;
  FDatasmithFacadePointLight *arg1 = (FDatasmithFacadePointLight *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadePointLight *)jarg1; 
  result = (float)((FDatasmithFacadePointLight const *)arg1)->GetAttenuationRadius();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadePointLight_SetAttenuationRadius(void * jarg1, float jarg2) {
  FDatasmithFacadePointLight *arg1 = (FDatasmithFacadePointLight *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadePointLight *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetAttenuationRadius(arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeSpotLight(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeSpotLight *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeSpotLight *)new FDatasmithFacadeSpotLight((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeSpotLight(void * jarg1) {
  FDatasmithFacadeSpotLight *arg1 = (FDatasmithFacadeSpotLight *) 0 ;
  
  arg1 = (FDatasmithFacadeSpotLight *)jarg1; 
  delete arg1;
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeSpotLight_GetInnerConeAngle(void * jarg1) {
  float jresult ;
  FDatasmithFacadeSpotLight *arg1 = (FDatasmithFacadeSpotLight *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeSpotLight *)jarg1; 
  result = (float)((FDatasmithFacadeSpotLight const *)arg1)->GetInnerConeAngle();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeSpotLight_SetInnerConeAngle(void * jarg1, float jarg2) {
  FDatasmithFacadeSpotLight *arg1 = (FDatasmithFacadeSpotLight *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeSpotLight *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetInnerConeAngle(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeSpotLight_GetOuterConeAngle(void * jarg1) {
  float jresult ;
  FDatasmithFacadeSpotLight *arg1 = (FDatasmithFacadeSpotLight *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeSpotLight *)jarg1; 
  result = (float)((FDatasmithFacadeSpotLight const *)arg1)->GetOuterConeAngle();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeSpotLight_SetOuterConeAngle(void * jarg1, float jarg2) {
  FDatasmithFacadeSpotLight *arg1 = (FDatasmithFacadeSpotLight *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeSpotLight *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetOuterConeAngle(arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeDirectionalLight(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeDirectionalLight *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeDirectionalLight *)new FDatasmithFacadeDirectionalLight((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeDirectionalLight(void * jarg1) {
  FDatasmithFacadeDirectionalLight *arg1 = (FDatasmithFacadeDirectionalLight *) 0 ;
  
  arg1 = (FDatasmithFacadeDirectionalLight *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeAreaLight(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeAreaLight *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeAreaLight *)new FDatasmithFacadeAreaLight((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeAreaLight(void * jarg1) {
  FDatasmithFacadeAreaLight *arg1 = (FDatasmithFacadeAreaLight *) 0 ;
  
  arg1 = (FDatasmithFacadeAreaLight *)jarg1; 
  delete arg1;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeAreaLight_GetLightShape(void * jarg1) {
  int jresult ;
  FDatasmithFacadeAreaLight *arg1 = (FDatasmithFacadeAreaLight *) 0 ;
  FDatasmithFacadeAreaLight::EAreaLightShape result;
  
  arg1 = (FDatasmithFacadeAreaLight *)jarg1; 
  result = (FDatasmithFacadeAreaLight::EAreaLightShape)((FDatasmithFacadeAreaLight const *)arg1)->GetLightShape();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeAreaLight_SetLightShape(void * jarg1, int jarg2) {
  FDatasmithFacadeAreaLight *arg1 = (FDatasmithFacadeAreaLight *) 0 ;
  FDatasmithFacadeAreaLight::EAreaLightShape arg2 ;
  
  arg1 = (FDatasmithFacadeAreaLight *)jarg1; 
  arg2 = (FDatasmithFacadeAreaLight::EAreaLightShape)jarg2; 
  (arg1)->SetLightShape(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeAreaLight_SetLightType(void * jarg1, int jarg2) {
  FDatasmithFacadeAreaLight *arg1 = (FDatasmithFacadeAreaLight *) 0 ;
  FDatasmithFacadeAreaLight::EAreaLightType arg2 ;
  
  arg1 = (FDatasmithFacadeAreaLight *)jarg1; 
  arg2 = (FDatasmithFacadeAreaLight::EAreaLightType)jarg2; 
  (arg1)->SetLightType(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeAreaLight_GetLightType(void * jarg1) {
  int jresult ;
  FDatasmithFacadeAreaLight *arg1 = (FDatasmithFacadeAreaLight *) 0 ;
  FDatasmithFacadeAreaLight::EAreaLightType result;
  
  arg1 = (FDatasmithFacadeAreaLight *)jarg1; 
  result = (FDatasmithFacadeAreaLight::EAreaLightType)((FDatasmithFacadeAreaLight const *)arg1)->GetLightType();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeAreaLight_SetWidth(void * jarg1, float jarg2) {
  FDatasmithFacadeAreaLight *arg1 = (FDatasmithFacadeAreaLight *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeAreaLight *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetWidth(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeAreaLight_GetWidth(void * jarg1) {
  float jresult ;
  FDatasmithFacadeAreaLight *arg1 = (FDatasmithFacadeAreaLight *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeAreaLight *)jarg1; 
  result = (float)((FDatasmithFacadeAreaLight const *)arg1)->GetWidth();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeAreaLight_SetLength(void * jarg1, float jarg2) {
  FDatasmithFacadeAreaLight *arg1 = (FDatasmithFacadeAreaLight *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeAreaLight *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetLength(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeAreaLight_GetLength(void * jarg1) {
  float jresult ;
  FDatasmithFacadeAreaLight *arg1 = (FDatasmithFacadeAreaLight *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeAreaLight *)jarg1; 
  result = (float)((FDatasmithFacadeAreaLight const *)arg1)->GetLength();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeLightmassPortal(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeLightmassPortal *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeLightmassPortal *)new FDatasmithFacadeLightmassPortal((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeLightmassPortal(void * jarg1) {
  FDatasmithFacadeLightmassPortal *arg1 = (FDatasmithFacadeLightmassPortal *) 0 ;
  
  arg1 = (FDatasmithFacadeLightmassPortal *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeActorMesh(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeActorMesh *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeActorMesh *)new FDatasmithFacadeActorMesh((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeActorMesh(void * jarg1) {
  FDatasmithFacadeActorMesh *arg1 = (FDatasmithFacadeActorMesh *) 0 ;
  
  arg1 = (FDatasmithFacadeActorMesh *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorMesh_SetMesh(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeActorMesh *arg1 = (FDatasmithFacadeActorMesh *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeActorMesh *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetMesh((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeActorMesh_GetMeshName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeActorMesh *arg1 = (FDatasmithFacadeActorMesh *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeActorMesh *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeActorMesh const *)arg1)->GetMeshName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorMesh_AddMaterialOverride__SWIG_0(void * jarg1, wchar_t * jarg2, int jarg3) {
  FDatasmithFacadeActorMesh *arg1 = (FDatasmithFacadeActorMesh *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  int32 arg3 ;
  
  arg1 = (FDatasmithFacadeActorMesh *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (int32)jarg3; 
  (arg1)->AddMaterialOverride((TCHAR const *)arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorMesh_AddMaterialOverride__SWIG_1(void * jarg1, void * jarg2) {
  FDatasmithFacadeActorMesh *arg1 = (FDatasmithFacadeActorMesh *) 0 ;
  FDatasmithFacadeMaterialID *arg2 = 0 ;
  
  arg1 = (FDatasmithFacadeActorMesh *)jarg1; 
  arg2 = (FDatasmithFacadeMaterialID *)jarg2;
  if (!arg2) {
    SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentNullException, "FDatasmithFacadeMaterialID & type is null", 0);
    return ;
  } 
  (arg1)->AddMaterialOverride(*arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeActorMesh_GetMaterialOverridesCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeActorMesh *arg1 = (FDatasmithFacadeActorMesh *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeActorMesh *)jarg1; 
  result = (int32)((FDatasmithFacadeActorMesh const *)arg1)->GetMaterialOverridesCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeActorMesh_GetMaterialOverride(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeActorMesh *arg1 = (FDatasmithFacadeActorMesh *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeMaterialID *result = 0 ;
  
  arg1 = (FDatasmithFacadeActorMesh *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeMaterialID *)(arg1)->GetNewMaterialOverride(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorMesh_RemoveMaterialOverride(void * jarg1, void * jarg2) {
  FDatasmithFacadeActorMesh *arg1 = (FDatasmithFacadeActorMesh *) 0 ;
  FDatasmithFacadeMaterialID *arg2 = 0 ;
  
  arg1 = (FDatasmithFacadeActorMesh *)jarg1; 
  arg2 = (FDatasmithFacadeMaterialID *)jarg2;
  if (!arg2) {
    SWIG_CSharpSetPendingExceptionArgument(SWIG_CSharpArgumentNullException, "FDatasmithFacadeMaterialID & type is null", 0);
    return ;
  } 
  (arg1)->RemoveMaterialOverride(*arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorMesh_ResetMaterialOverrides(void * jarg1) {
  FDatasmithFacadeActorMesh *arg1 = (FDatasmithFacadeActorMesh *) 0 ;
  
  arg1 = (FDatasmithFacadeActorMesh *)jarg1; 
  (arg1)->ResetMaterialOverrides();
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeBaseAnimation_IsSubType(void * jarg1, int jarg2) {
  unsigned int jresult ;
  FDatasmithFacadeBaseAnimation *arg1 = (FDatasmithFacadeBaseAnimation *) 0 ;
  EDatasmithFacadeElementAnimationSubType arg2 ;
  bool result;
  
  arg1 = (FDatasmithFacadeBaseAnimation *)jarg1; 
  arg2 = (EDatasmithFacadeElementAnimationSubType)jarg2; 
  result = (bool)((FDatasmithFacadeBaseAnimation const *)arg1)->IsSubType(arg2);
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeBaseAnimation_SetCompletionMode(void * jarg1, int jarg2) {
  FDatasmithFacadeBaseAnimation *arg1 = (FDatasmithFacadeBaseAnimation *) 0 ;
  EDatasmithFacadeCompletionMode arg2 ;
  
  arg1 = (FDatasmithFacadeBaseAnimation *)jarg1; 
  arg2 = (EDatasmithFacadeCompletionMode)jarg2; 
  (arg1)->SetCompletionMode(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeBaseAnimation_GetCompletionMode(void * jarg1) {
  int jresult ;
  FDatasmithFacadeBaseAnimation *arg1 = (FDatasmithFacadeBaseAnimation *) 0 ;
  EDatasmithFacadeCompletionMode result;
  
  arg1 = (FDatasmithFacadeBaseAnimation *)jarg1; 
  result = (EDatasmithFacadeCompletionMode)((FDatasmithFacadeBaseAnimation const *)arg1)->GetCompletionMode();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeBaseAnimation(void * jarg1) {
  FDatasmithFacadeBaseAnimation *arg1 = (FDatasmithFacadeBaseAnimation *) 0 ;
  
  arg1 = (FDatasmithFacadeBaseAnimation *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeTransformAnimation(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeTransformAnimation *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeTransformAnimation *)new FDatasmithFacadeTransformAnimation((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTransformAnimation_AddFrame(void * jarg1, int jarg2, int jarg3, float jarg4, float jarg5, float jarg6) {
  FDatasmithFacadeTransformAnimation *arg1 = (FDatasmithFacadeTransformAnimation *) 0 ;
  EDatasmithFacadeAnimationTransformType arg2 ;
  int32 arg3 ;
  float arg4 ;
  float arg5 ;
  float arg6 ;
  
  arg1 = (FDatasmithFacadeTransformAnimation *)jarg1; 
  arg2 = (EDatasmithFacadeAnimationTransformType)jarg2; 
  arg3 = (int32)jarg3; 
  arg4 = (float)jarg4; 
  arg5 = (float)jarg5; 
  arg6 = (float)jarg6; 
  (arg1)->AddFrame(arg2,arg3,arg4,arg5,arg6);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeTransformAnimation_GetFramesCount(void * jarg1, int jarg2) {
  int jresult ;
  FDatasmithFacadeTransformAnimation *arg1 = (FDatasmithFacadeTransformAnimation *) 0 ;
  EDatasmithFacadeAnimationTransformType arg2 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeTransformAnimation *)jarg1; 
  arg2 = (EDatasmithFacadeAnimationTransformType)jarg2; 
  result = (int32)((FDatasmithFacadeTransformAnimation const *)arg1)->GetFramesCount(arg2);
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTransformAnimation_SetCurveInterpMode(void * jarg1, int jarg2, int jarg3) {
  FDatasmithFacadeTransformAnimation *arg1 = (FDatasmithFacadeTransformAnimation *) 0 ;
  EDatasmithFacadeAnimationTransformType arg2 ;
  EDatasmithFacadeCurveInterpMode arg3 ;
  
  arg1 = (FDatasmithFacadeTransformAnimation *)jarg1; 
  arg2 = (EDatasmithFacadeAnimationTransformType)jarg2; 
  arg3 = (EDatasmithFacadeCurveInterpMode)jarg3; 
  (arg1)->SetCurveInterpMode(arg2,arg3);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeTransformAnimation_GetCurveInterpMode(void * jarg1, int jarg2) {
  int jresult ;
  FDatasmithFacadeTransformAnimation *arg1 = (FDatasmithFacadeTransformAnimation *) 0 ;
  EDatasmithFacadeAnimationTransformType arg2 ;
  EDatasmithFacadeCurveInterpMode result;
  
  arg1 = (FDatasmithFacadeTransformAnimation *)jarg1; 
  arg2 = (EDatasmithFacadeAnimationTransformType)jarg2; 
  result = (EDatasmithFacadeCurveInterpMode)((FDatasmithFacadeTransformAnimation const *)arg1)->GetCurveInterpMode(arg2);
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTransformAnimation_GetFrame(void * jarg1, int jarg2, int jarg3, int * jarg4, double * jarg5, double * jarg6, double * jarg7) {
  FDatasmithFacadeTransformAnimation *arg1 = (FDatasmithFacadeTransformAnimation *) 0 ;
  EDatasmithFacadeAnimationTransformType arg2 ;
  int32 arg3 ;
  int32 *arg4 = 0 ;
  double *arg5 = 0 ;
  double *arg6 = 0 ;
  double *arg7 = 0 ;
  
  arg1 = (FDatasmithFacadeTransformAnimation *)jarg1; 
  arg2 = (EDatasmithFacadeAnimationTransformType)jarg2; 
  arg3 = (int32)jarg3; 
  arg4 = (int32 *)jarg4; 
  arg5 = (double *)jarg5; 
  arg6 = (double *)jarg6; 
  arg7 = (double *)jarg7; 
  ((FDatasmithFacadeTransformAnimation const *)arg1)->GetFrame(arg2,arg3,*arg4,*arg5,*arg6,*arg7);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTransformAnimation_RemoveFrame(void * jarg1, int jarg2, int jarg3) {
  FDatasmithFacadeTransformAnimation *arg1 = (FDatasmithFacadeTransformAnimation *) 0 ;
  EDatasmithFacadeAnimationTransformType arg2 ;
  int32 arg3 ;
  
  arg1 = (FDatasmithFacadeTransformAnimation *)jarg1; 
  arg2 = (EDatasmithFacadeAnimationTransformType)jarg2; 
  arg3 = (int32)jarg3; 
  (arg1)->RemoveFrame(arg2,arg3);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeTransformAnimation_GetEnabledTransformChannels(void * jarg1) {
  int jresult ;
  FDatasmithFacadeTransformAnimation *arg1 = (FDatasmithFacadeTransformAnimation *) 0 ;
  EDatasmithFacadeTransformChannels result;
  
  arg1 = (FDatasmithFacadeTransformAnimation *)jarg1; 
  result = (EDatasmithFacadeTransformChannels)((FDatasmithFacadeTransformAnimation const *)arg1)->GetEnabledTransformChannels();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeTransformAnimation_SetEnabledTransformChannels(void * jarg1, int jarg2) {
  FDatasmithFacadeTransformAnimation *arg1 = (FDatasmithFacadeTransformAnimation *) 0 ;
  EDatasmithFacadeTransformChannels arg2 ;
  
  arg1 = (FDatasmithFacadeTransformAnimation *)jarg1; 
  arg2 = (EDatasmithFacadeTransformChannels)jarg2; 
  (arg1)->SetEnabledTransformChannels(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeTransformAnimation(void * jarg1) {
  FDatasmithFacadeTransformAnimation *arg1 = (FDatasmithFacadeTransformAnimation *) 0 ;
  
  arg1 = (FDatasmithFacadeTransformAnimation *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeVisibilityAnimation(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeVisibilityAnimation *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeVisibilityAnimation *)new FDatasmithFacadeVisibilityAnimation((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeVisibilityAnimation_AddFrame(void * jarg1, int jarg2, unsigned int jarg3) {
  FDatasmithFacadeVisibilityAnimation *arg1 = (FDatasmithFacadeVisibilityAnimation *) 0 ;
  int32 arg2 ;
  bool arg3 ;
  
  arg1 = (FDatasmithFacadeVisibilityAnimation *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = jarg3 ? true : false; 
  (arg1)->AddFrame(arg2,arg3);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeVisibilityAnimation_GetFramesCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeVisibilityAnimation *arg1 = (FDatasmithFacadeVisibilityAnimation *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeVisibilityAnimation *)jarg1; 
  result = (int32)((FDatasmithFacadeVisibilityAnimation const *)arg1)->GetFramesCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeVisibilityAnimation_SetCurveInterpMode(void * jarg1, int jarg2) {
  FDatasmithFacadeVisibilityAnimation *arg1 = (FDatasmithFacadeVisibilityAnimation *) 0 ;
  EDatasmithFacadeCurveInterpMode arg2 ;
  
  arg1 = (FDatasmithFacadeVisibilityAnimation *)jarg1; 
  arg2 = (EDatasmithFacadeCurveInterpMode)jarg2; 
  (arg1)->SetCurveInterpMode(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeVisibilityAnimation_GetCurveInterpMode(void * jarg1) {
  int jresult ;
  FDatasmithFacadeVisibilityAnimation *arg1 = (FDatasmithFacadeVisibilityAnimation *) 0 ;
  EDatasmithFacadeCurveInterpMode result;
  
  arg1 = (FDatasmithFacadeVisibilityAnimation *)jarg1; 
  result = (EDatasmithFacadeCurveInterpMode)((FDatasmithFacadeVisibilityAnimation const *)arg1)->GetCurveInterpMode();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeVisibilityAnimation_GetFrame(void * jarg1, int jarg2, int * jarg3, unsigned int * jarg4) {
  FDatasmithFacadeVisibilityAnimation *arg1 = (FDatasmithFacadeVisibilityAnimation *) 0 ;
  int32 arg2 ;
  int32 *arg3 = 0 ;
  bool *arg4 = 0 ;
  
  arg1 = (FDatasmithFacadeVisibilityAnimation *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (int32 *)jarg3; 
  *jarg4 = 0; 
  arg4 = (bool *)jarg4; 
  ((FDatasmithFacadeVisibilityAnimation const *)arg1)->GetFrame(arg2,*arg3,*arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeVisibilityAnimation_RemoveFrame(void * jarg1, int jarg2) {
  FDatasmithFacadeVisibilityAnimation *arg1 = (FDatasmithFacadeVisibilityAnimation *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeVisibilityAnimation *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->RemoveFrame(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeVisibilityAnimation_SetPropagateToChildren(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeVisibilityAnimation *arg1 = (FDatasmithFacadeVisibilityAnimation *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeVisibilityAnimation *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->SetPropagateToChildren(arg2);
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeVisibilityAnimation_GetPropagateToChildren(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeVisibilityAnimation *arg1 = (FDatasmithFacadeVisibilityAnimation *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeVisibilityAnimation *)jarg1; 
  result = (bool)((FDatasmithFacadeVisibilityAnimation const *)arg1)->GetPropagateToChildren();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeVisibilityAnimation(void * jarg1) {
  FDatasmithFacadeVisibilityAnimation *arg1 = (FDatasmithFacadeVisibilityAnimation *) 0 ;
  
  arg1 = (FDatasmithFacadeVisibilityAnimation *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeSubsequenceAnimation(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeSubsequenceAnimation *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeSubsequenceAnimation *)new FDatasmithFacadeSubsequenceAnimation((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeSubsequenceAnimation_GetStartTime(void * jarg1) {
  int jresult ;
  FDatasmithFacadeSubsequenceAnimation *arg1 = (FDatasmithFacadeSubsequenceAnimation *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeSubsequenceAnimation *)jarg1; 
  result = (int32)((FDatasmithFacadeSubsequenceAnimation const *)arg1)->GetStartTime();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeSubsequenceAnimation_SetStartTime(void * jarg1, int jarg2) {
  FDatasmithFacadeSubsequenceAnimation *arg1 = (FDatasmithFacadeSubsequenceAnimation *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeSubsequenceAnimation *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->SetStartTime(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeSubsequenceAnimation_GetDuration(void * jarg1) {
  int jresult ;
  FDatasmithFacadeSubsequenceAnimation *arg1 = (FDatasmithFacadeSubsequenceAnimation *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeSubsequenceAnimation *)jarg1; 
  result = (int32)((FDatasmithFacadeSubsequenceAnimation const *)arg1)->GetDuration();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeSubsequenceAnimation_SetDuration(void * jarg1, int jarg2) {
  FDatasmithFacadeSubsequenceAnimation *arg1 = (FDatasmithFacadeSubsequenceAnimation *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeSubsequenceAnimation *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->SetDuration(arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeSubsequenceAnimation_GetTimeScale(void * jarg1) {
  float jresult ;
  FDatasmithFacadeSubsequenceAnimation *arg1 = (FDatasmithFacadeSubsequenceAnimation *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeSubsequenceAnimation *)jarg1; 
  result = (float)((FDatasmithFacadeSubsequenceAnimation const *)arg1)->GetTimeScale();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeSubsequenceAnimation_SetTimeScale(void * jarg1, float jarg2) {
  FDatasmithFacadeSubsequenceAnimation *arg1 = (FDatasmithFacadeSubsequenceAnimation *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeSubsequenceAnimation *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetTimeScale(arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeSubsequenceAnimation_GetNewSubsequence(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeSubsequenceAnimation *arg1 = (FDatasmithFacadeSubsequenceAnimation *) 0 ;
  FDatasmithFacadeLevelSequence *result = 0 ;
  
  arg1 = (FDatasmithFacadeSubsequenceAnimation *)jarg1; 
  result = (FDatasmithFacadeLevelSequence *)((FDatasmithFacadeSubsequenceAnimation const *)arg1)->GetNewSubsequence();
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeSubsequenceAnimation_SetSubsequence(void * jarg1, void * jarg2) {
  FDatasmithFacadeSubsequenceAnimation *arg1 = (FDatasmithFacadeSubsequenceAnimation *) 0 ;
  FDatasmithFacadeLevelSequence *arg2 = (FDatasmithFacadeLevelSequence *) 0 ;
  
  arg1 = (FDatasmithFacadeSubsequenceAnimation *)jarg1; 
  arg2 = (FDatasmithFacadeLevelSequence *)jarg2; 
  (arg1)->SetSubsequence(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeSubsequenceAnimation(void * jarg1) {
  FDatasmithFacadeSubsequenceAnimation *arg1 = (FDatasmithFacadeSubsequenceAnimation *) 0 ;
  
  arg1 = (FDatasmithFacadeSubsequenceAnimation *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeLevelSequence(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeLevelSequence *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeLevelSequence *)new FDatasmithFacadeLevelSequence((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeLevelSequence_GetFile(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeLevelSequence *arg1 = (FDatasmithFacadeLevelSequence *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeLevelSequence *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeLevelSequence const *)arg1)->GetFile();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeLevelSequence_SetFile(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeLevelSequence *arg1 = (FDatasmithFacadeLevelSequence *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeLevelSequence *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetFile((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeLevelSequence_InternalGetFileHash(void * jarg1, void * jarg2, unsigned long jarg3) {
  FDatasmithFacadeLevelSequence *arg1 = (FDatasmithFacadeLevelSequence *) 0 ;
  TCHAR *arg2 ;
  size_t arg3 ;
  
  arg1 = (FDatasmithFacadeLevelSequence *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (size_t)jarg3; 
  ((FDatasmithFacadeLevelSequence const *)arg1)->GetFileHash(arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeLevelSequence_SetFileHash(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeLevelSequence *arg1 = (FDatasmithFacadeLevelSequence *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeLevelSequence *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetFileHash((TCHAR const *)arg2);
}


SWIGEXPORT float SWIGSTDCALL CSharp_FDatasmithFacadeLevelSequence_GetFrameRate(void * jarg1) {
  float jresult ;
  FDatasmithFacadeLevelSequence *arg1 = (FDatasmithFacadeLevelSequence *) 0 ;
  float result;
  
  arg1 = (FDatasmithFacadeLevelSequence *)jarg1; 
  result = (float)((FDatasmithFacadeLevelSequence const *)arg1)->GetFrameRate();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeLevelSequence_SetFrameRate(void * jarg1, float jarg2) {
  FDatasmithFacadeLevelSequence *arg1 = (FDatasmithFacadeLevelSequence *) 0 ;
  float arg2 ;
  
  arg1 = (FDatasmithFacadeLevelSequence *)jarg1; 
  arg2 = (float)jarg2; 
  (arg1)->SetFrameRate(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeLevelSequence_AddAnimation(void * jarg1, void * jarg2) {
  FDatasmithFacadeLevelSequence *arg1 = (FDatasmithFacadeLevelSequence *) 0 ;
  FDatasmithFacadeTransformAnimation *arg2 = (FDatasmithFacadeTransformAnimation *) 0 ;
  
  arg1 = (FDatasmithFacadeLevelSequence *)jarg1; 
  arg2 = (FDatasmithFacadeTransformAnimation *)jarg2; 
  (arg1)->AddAnimation(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeLevelSequence_GetAnimationsCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeLevelSequence *arg1 = (FDatasmithFacadeLevelSequence *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeLevelSequence *)jarg1; 
  result = (int32)((FDatasmithFacadeLevelSequence const *)arg1)->GetAnimationsCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeLevelSequence_GetTransformAnimation(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeLevelSequence *arg1 = (FDatasmithFacadeLevelSequence *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeTransformAnimation *result = 0 ;
  
  arg1 = (FDatasmithFacadeLevelSequence *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeTransformAnimation *)(arg1)->GetNewTransformAnimation(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeLevelSequence_GetVisibilityAnimation(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeLevelSequence *arg1 = (FDatasmithFacadeLevelSequence *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeVisibilityAnimation *result = 0 ;
  
  arg1 = (FDatasmithFacadeLevelSequence *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeVisibilityAnimation *)(arg1)->GetNewVisibilityAnimation(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeLevelSequence_GetSubsequenceAnimation(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeLevelSequence *arg1 = (FDatasmithFacadeLevelSequence *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeSubsequenceAnimation *result = 0 ;
  
  arg1 = (FDatasmithFacadeLevelSequence *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeSubsequenceAnimation *)(arg1)->GetNewSubsequenceAnimation(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeLevelSequence_RemoveAnimation(void * jarg1, void * jarg2) {
  FDatasmithFacadeLevelSequence *arg1 = (FDatasmithFacadeLevelSequence *) 0 ;
  FDatasmithFacadeBaseAnimation *arg2 = (FDatasmithFacadeBaseAnimation *) 0 ;
  
  arg1 = (FDatasmithFacadeLevelSequence *)jarg1; 
  arg2 = (FDatasmithFacadeBaseAnimation *)jarg2; 
  (arg1)->RemoveAnimation(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeLevelSequence(void * jarg1) {
  FDatasmithFacadeLevelSequence *arg1 = (FDatasmithFacadeLevelSequence *) 0 ;
  
  arg1 = (FDatasmithFacadeLevelSequence *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeScene(wchar_t * jarg1, wchar_t * jarg2, wchar_t * jarg3, wchar_t * jarg4) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  TCHAR *arg3 = (TCHAR *) 0 ;
  TCHAR *arg4 = (TCHAR *) 0 ;
  FDatasmithFacadeScene *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = (TCHAR *)jarg3; 
  arg4 = (TCHAR *)jarg4; 
  result = (FDatasmithFacadeScene *)new FDatasmithFacadeScene((TCHAR const *)arg1,(TCHAR const *)arg2,(TCHAR const *)arg3,(TCHAR const *)arg4);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_AddActor(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeActor *arg2 = (FDatasmithFacadeActor *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeActor *)jarg2; 
  (arg1)->AddActor(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetActorsCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (int32)((FDatasmithFacadeScene const *)arg1)->GetActorsCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetActor(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeActor *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeActor *)(arg1)->GetNewActor(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveActor__SWIG_0(void * jarg1, void * jarg2, int jarg3) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeActor *arg2 = (FDatasmithFacadeActor *) 0 ;
  FDatasmithFacadeScene::EActorRemovalRule arg3 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeActor *)jarg2; 
  arg3 = (FDatasmithFacadeScene::EActorRemovalRule)jarg3; 
  (arg1)->RemoveActor(arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveActor__SWIG_1(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeActor *arg2 = (FDatasmithFacadeActor *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeActor *)jarg2; 
  (arg1)->RemoveActor(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveActorAt__SWIG_0(void * jarg1, int jarg2, int jarg3) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeScene::EActorRemovalRule arg3 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  arg3 = (FDatasmithFacadeScene::EActorRemovalRule)jarg3; 
  (arg1)->RemoveActorAt(arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveActorAt__SWIG_1(void * jarg1, int jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->RemoveActorAt(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_AddMaterial(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeBaseMaterial *arg2 = (FDatasmithFacadeBaseMaterial *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeBaseMaterial *)jarg2; 
  (arg1)->AddMaterial(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetMaterialsCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (int32)((FDatasmithFacadeScene const *)arg1)->GetMaterialsCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetMaterial(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeBaseMaterial *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeBaseMaterial *)(arg1)->GetNewMaterial(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveMaterial(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeBaseMaterial *arg2 = (FDatasmithFacadeBaseMaterial *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeBaseMaterial *)jarg2; 
  (arg1)->RemoveMaterial(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveMaterialAt(void * jarg1, int jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->RemoveMaterialAt(arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeScene_ExportDatasmithMesh__SWIG_0(void * jarg1, void * jarg2, void * jarg3) {
  void * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeMesh *arg2 = (FDatasmithFacadeMesh *) 0 ;
  FDatasmithFacadeMesh *arg3 = (FDatasmithFacadeMesh *) 0 ;
  FDatasmithFacadeMeshElement *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeMesh *)jarg2; 
  arg3 = (FDatasmithFacadeMesh *)jarg3; 
  result = (FDatasmithFacadeMeshElement *)(arg1)->ExportDatasmithMesh(arg2,arg3);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeScene_ExportDatasmithMesh__SWIG_1(void * jarg1, void * jarg2) {
  void * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeMesh *arg2 = (FDatasmithFacadeMesh *) 0 ;
  FDatasmithFacadeMeshElement *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeMesh *)jarg2; 
  result = (FDatasmithFacadeMeshElement *)(arg1)->ExportDatasmithMesh(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeScene_ExportDatasmithMesh__SWIG_2(void * jarg1, void * jarg2, void * jarg3, void * jarg4) {
  unsigned int jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeMeshElement *arg2 = (FDatasmithFacadeMeshElement *) 0 ;
  FDatasmithFacadeMesh *arg3 = (FDatasmithFacadeMesh *) 0 ;
  FDatasmithFacadeMesh *arg4 = (FDatasmithFacadeMesh *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeMeshElement *)jarg2; 
  arg3 = (FDatasmithFacadeMesh *)jarg3; 
  arg4 = (FDatasmithFacadeMesh *)jarg4; 
  result = (bool)(arg1)->ExportDatasmithMesh(arg2,arg3,arg4);
  jresult = result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeScene_ExportDatasmithMesh__SWIG_3(void * jarg1, void * jarg2, void * jarg3) {
  unsigned int jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeMeshElement *arg2 = (FDatasmithFacadeMeshElement *) 0 ;
  FDatasmithFacadeMesh *arg3 = (FDatasmithFacadeMesh *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeMeshElement *)jarg2; 
  arg3 = (FDatasmithFacadeMesh *)jarg3; 
  result = (bool)(arg1)->ExportDatasmithMesh(arg2,arg3);
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_AddMesh(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeMeshElement *arg2 = (FDatasmithFacadeMeshElement *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeMeshElement *)jarg2; 
  (arg1)->AddMesh(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetMeshesCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (int32)((FDatasmithFacadeScene const *)arg1)->GetMeshesCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetMesh(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeMeshElement *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeMeshElement *)(arg1)->GetNewMesh(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveMesh(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeMeshElement *arg2 = (FDatasmithFacadeMeshElement *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeMeshElement *)jarg2; 
  (arg1)->RemoveMesh(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveMeshAt(void * jarg1, int jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->RemoveMeshAt(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_AddTexture(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeTexture *arg2 = (FDatasmithFacadeTexture *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeTexture *)jarg2; 
  (arg1)->AddTexture(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetTexturesCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (int32)((FDatasmithFacadeScene const *)arg1)->GetTexturesCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetTexture(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeTexture *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeTexture *)(arg1)->GetNewTexture(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveTexture(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeTexture *arg2 = (FDatasmithFacadeTexture *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeTexture *)jarg2; 
  (arg1)->RemoveTexture(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveTextureAt(void * jarg1, int jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->RemoveTextureAt(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_AddLevelVariantSets(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeLevelVariantSets *arg2 = (FDatasmithFacadeLevelVariantSets *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeLevelVariantSets *)jarg2; 
  (arg1)->AddLevelVariantSets(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetLevelVariantSetsCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (int32)((FDatasmithFacadeScene const *)arg1)->GetLevelVariantSetsCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetLevelVariantSets(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeLevelVariantSets *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeLevelVariantSets *)(arg1)->GetNewLevelVariantSets(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveLevelVariantSets(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeLevelVariantSets *arg2 = (FDatasmithFacadeLevelVariantSets *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeLevelVariantSets *)jarg2; 
  (arg1)->RemoveLevelVariantSets(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveLevelVariantSetsAt(void * jarg1, int jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->RemoveLevelVariantSetsAt(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_AddLevelSequence(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeLevelSequence *arg2 = (FDatasmithFacadeLevelSequence *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeLevelSequence *)jarg2; 
  (arg1)->AddLevelSequence(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetLevelSequencesCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (int32)((FDatasmithFacadeScene const *)arg1)->GetLevelSequencesCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetLevelSequence(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeLevelSequence *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeLevelSequence *)(arg1)->GetNewLevelSequence(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveLevelSequence(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeLevelSequence *arg2 = (FDatasmithFacadeLevelSequence *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeLevelSequence *)jarg2; 
  (arg1)->RemoveLevelSequence(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveLevelSequenceAt(void * jarg1, int jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->RemoveLevelSequenceAt(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_AddMetaData(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeMetaData *arg2 = (FDatasmithFacadeMetaData *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeMetaData *)jarg2; 
  (arg1)->AddMetaData(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetMetaDataCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (int32)((FDatasmithFacadeScene const *)arg1)->GetMetaDataCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetMetaData__SWIG_0(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeMetaData *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeMetaData *)(arg1)->GetNewMetaData(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetMetaData__SWIG_1(void * jarg1, void * jarg2) {
  void * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeElement *arg2 = (FDatasmithFacadeElement *) 0 ;
  FDatasmithFacadeMetaData *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeElement *)jarg2; 
  result = (FDatasmithFacadeMetaData *)(arg1)->GetNewMetaData(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveMetaData(void * jarg1, void * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  FDatasmithFacadeMetaData *arg2 = (FDatasmithFacadeMetaData *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (FDatasmithFacadeMetaData *)jarg2; 
  (arg1)->RemoveMetaData(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_RemoveMetaDataAt(void * jarg1, int jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->RemoveMetaDataAt(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_SetName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetName((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeScene const *)arg1)->GetName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_SetOutputPath(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetOutputPath((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetOutputPath(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeScene const *)arg1)->GetOutputPath();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetAssetsOutputPath(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeScene const *)arg1)->GetAssetsOutputPath();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetGeolocation(void * jarg1, double * jarg2, double * jarg3, double * jarg4) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  double *arg2 = 0 ;
  double *arg3 = 0 ;
  double *arg4 = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (double *)jarg2; 
  arg3 = (double *)jarg3; 
  arg4 = (double *)jarg4; 
  ((FDatasmithFacadeScene const *)arg1)->GetGeolocation(*arg2,*arg3,*arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_SetGeolocationLatitude(void * jarg1, double jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  double arg2 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (double)jarg2; 
  (arg1)->SetGeolocationLatitude(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_SetGeolocationLongitude(void * jarg1, double jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  double arg2 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (double)jarg2; 
  (arg1)->SetGeolocationLongitude(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_SetGeolocationElevation(void * jarg1, double jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  double arg2 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (double)jarg2; 
  (arg1)->SetGeolocationElevation(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_PreExport(void * jarg1) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  (arg1)->PreExport();
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_CleanUp(void * jarg1) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  (arg1)->CleanUp();
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_Shutdown() {
  FDatasmithFacadeScene::Shutdown();
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeScene_ExportScene__SWIG_0(void * jarg1, wchar_t * jarg2, unsigned int jarg3) {
  unsigned int jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  bool arg3 ;
  bool result;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  arg3 = jarg3 ? true : false; 
  result = (bool)(arg1)->ExportScene((TCHAR const *)arg2,arg3);
  jresult = result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeScene_ExportScene__SWIG_1(void * jarg1, wchar_t * jarg2) {
  unsigned int jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  result = (bool)(arg1)->ExportScene((TCHAR const *)arg2);
  jresult = result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeScene_ExportScene__SWIG_2(void * jarg1, unsigned int jarg2) {
  unsigned int jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  bool arg2 ;
  bool result;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = jarg2 ? true : false; 
  result = (bool)(arg1)->ExportScene(arg2);
  jresult = result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeScene_ExportScene__SWIG_3(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (bool)(arg1)->ExportScene();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_SerializeLevelSequences(void * jarg1) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  (arg1)->SerializeLevelSequences();
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_SetLabel(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetLabel((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetLabel(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeScene const *)arg1)->GetLabel();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_Reset(void * jarg1) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  (arg1)->Reset();
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetHost(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeScene const *)arg1)->GetHost();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_SetHost(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetHost((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetVendor(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeScene const *)arg1)->GetVendor();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_SetVendor(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetVendor((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetProductName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeScene const *)arg1)->GetProductName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_SetProductName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetProductName((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeScene_GetProductVersion(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeScene const *)arg1)->GetProductVersion();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeScene_SetProductVersion(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetProductVersion((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeScene(void * jarg1) {
  FDatasmithFacadeScene *arg1 = (FDatasmithFacadeScene *) 0 ;
  
  arg1 = (FDatasmithFacadeScene *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeLog() {
  void * jresult ;
  FDatasmithFacadeLog *result = 0 ;
  
  result = (FDatasmithFacadeLog *)new FDatasmithFacadeLog();
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeLog_AddLine(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeLog *arg1 = (FDatasmithFacadeLog *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeLog *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->AddLine((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeLog_MoreIndentation(void * jarg1) {
  FDatasmithFacadeLog *arg1 = (FDatasmithFacadeLog *) 0 ;
  
  arg1 = (FDatasmithFacadeLog *)jarg1; 
  (arg1)->MoreIndentation();
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeLog_LessIndentation(void * jarg1) {
  FDatasmithFacadeLog *arg1 = (FDatasmithFacadeLog *) 0 ;
  
  arg1 = (FDatasmithFacadeLog *)jarg1; 
  (arg1)->LessIndentation();
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeLog_WriteFile(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeLog *arg1 = (FDatasmithFacadeLog *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeLog *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  ((FDatasmithFacadeLog const *)arg1)->WriteFile((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeLog(void * jarg1) {
  FDatasmithFacadeLog *arg1 = (FDatasmithFacadeLog *) 0 ;
  
  arg1 = (FDatasmithFacadeLog *)jarg1; 
  delete arg1;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointId_GetName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId const *)arg1)->GetName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointId_GetId(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *) 0 ;
  SwigValueWrapper< FGuid > result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *)jarg1; 
  result = ((FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId const *)arg1)->GetId();
  jresult = new FGuid((const FGuid &)result); 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointId_IsPublic(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *)jarg1; 
  result = (bool)((FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId const *)arg1)->IsPublic();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointId(void * jarg1) {
  FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *) 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *)jarg1; 
  delete arg1;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo const *)arg1)->GetName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetNumberOfDestinations(void * jarg1) {
  int jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *)jarg1; 
  result = (int32)((FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo const *)arg1)->GetNumberOfDestinations();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetDestination(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *result = 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *)((FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo const *)arg1)->GetNewDestination(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetNumberOfSources(void * jarg1) {
  int jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *)jarg1; 
  result = (int32)((FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo const *)arg1)->GetNumberOfSources();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetSource(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *result = 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointId *)((FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo const *)arg1)->GetNewSource(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetUserName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo const *)arg1)->GetUserName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetExecutableName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo const *)arg1)->GetExecutableName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetComputerName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo const *)arg1)->GetComputerName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_IsLocal(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *)jarg1; 
  result = (bool)((FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo const *)arg1)->IsLocal();
  jresult = result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetProcessId(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *) 0 ;
  uint32 result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *)jarg1; 
  result = (uint32)((FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo const *)arg1)->GetProcessId();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo(void * jarg1) {
  FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *) 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointInfo_GetEndpointAddress(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *) 0 ;
  SwigValueWrapper< FMessageAddress > result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *)jarg1; 
  result = ((FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo const *)arg1)->GetEndpointAddress();
  jresult = new FMessageAddress((const FMessageAddress &)result); 
  return jresult;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointInfo_GetName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo const *)arg1)->GetName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointInfo_IsSource(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *)jarg1; 
  result = (bool)((FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo const *)arg1)->IsSource();
  jresult = result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointInfo_IsOnThisEndpoint(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *)jarg1; 
  result = (bool)((FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo const *)arg1)->IsOnThisEndpoint();
  jresult = result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointInfo_IsPublic(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *)jarg1; 
  result = (bool)((FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo const *)arg1)->IsPublic();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointInfo(void * jarg1) {
  FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *) 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *)jarg1; 
  delete arg1;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeStreamInfo_GetStreamId(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *) 0 ;
  uint32 result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *)jarg1; 
  result = (uint32)((FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo const *)arg1)->GetStreamId();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeStreamInfo_GetSource(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *) 0 ;
  SwigValueWrapper< FGuid > result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *)jarg1; 
  result = ((FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo const *)arg1)->GetSource();
  jresult = new FGuid((const FGuid &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeStreamInfo_GetDestination(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *) 0 ;
  SwigValueWrapper< FGuid > result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *)jarg1; 
  result = ((FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo const *)arg1)->GetDestination();
  jresult = new FGuid((const FGuid &)result); 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_FDatasmithFacadeStreamInfo_IsActive(void * jarg1) {
  unsigned int jresult ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *)jarg1; 
  result = (bool)((FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo const *)arg1)->IsActive();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeRawInfo_FDatasmithFacadeStreamInfo(void * jarg1) {
  FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *) 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_GetThisEndpointAddress(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeRawInfo *arg1 = (FDatasmithFacadeRawInfo *) 0 ;
  SwigValueWrapper< FMessageAddress > result;
  
  arg1 = (FDatasmithFacadeRawInfo *)jarg1; 
  result = ((FDatasmithFacadeRawInfo const *)arg1)->GetThisEndpointAddress();
  jresult = new FMessageAddress((const FMessageAddress &)result); 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_GetEndpointInfo(void * jarg1, void * jarg2) {
  void * jresult ;
  FDatasmithFacadeRawInfo *arg1 = (FDatasmithFacadeRawInfo *) 0 ;
  FMessageAddress *arg2 = (FMessageAddress *) 0 ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *result = 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo *)jarg1; 
  arg2 = (FMessageAddress *)jarg2; 
  result = (FDatasmithFacadeRawInfo::FDatasmithFacadeEndpointInfo *)((FDatasmithFacadeRawInfo const *)arg1)->GetNewEndpointInfo((FMessageAddress const *)arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_GetDataPointsInfo(void * jarg1, void * jarg2) {
  void * jresult ;
  FDatasmithFacadeRawInfo *arg1 = (FDatasmithFacadeRawInfo *) 0 ;
  FGuid *arg2 = (FGuid *) 0 ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *result = 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo *)jarg1; 
  arg2 = (FGuid *)jarg2; 
  result = (FDatasmithFacadeRawInfo::FDatasmithFacadeDataPointInfo *)((FDatasmithFacadeRawInfo const *)arg1)->GetNewDataPointsInfo((FGuid const *)arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_GetNumberOfStreamsInfo(void * jarg1) {
  int jresult ;
  FDatasmithFacadeRawInfo *arg1 = (FDatasmithFacadeRawInfo *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeRawInfo *)jarg1; 
  result = (int32)((FDatasmithFacadeRawInfo const *)arg1)->GetNumberOfStreamsInfo();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeRawInfo_GetStreamInfo(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeRawInfo *arg1 = (FDatasmithFacadeRawInfo *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *result = 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeRawInfo::FDatasmithFacadeStreamInfo *)((FDatasmithFacadeRawInfo const *)arg1)->GetNewStreamInfo(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeRawInfo(void * jarg1) {
  FDatasmithFacadeRawInfo *arg1 = (FDatasmithFacadeRawInfo *) 0 ;
  
  arg1 = (FDatasmithFacadeRawInfo *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeEndpointObserver() {
  void * jresult ;
  FDatasmithFacadeEndpointObserver *result = 0 ;
  
  result = (FDatasmithFacadeEndpointObserver *)new FDatasmithFacadeEndpointObserver();
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeEndpointObserver_RegisterOnStateChangedDelegateInternal(void * jarg1, void * jarg2) {
  FDatasmithFacadeEndpointObserver *arg1 = (FDatasmithFacadeEndpointObserver *) 0 ;
  FDatasmithFacadeEndpointObserver::OnStateChangedDelegate arg2 = (FDatasmithFacadeEndpointObserver::OnStateChangedDelegate) 0 ;
  
  arg1 = (FDatasmithFacadeEndpointObserver *)jarg1; 
  arg2 = (FDatasmithFacadeEndpointObserver::OnStateChangedDelegate)jarg2; 
  (arg1)->RegisterOnStateChangedDelegateInternal(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeEndpointObserver_UnregisterOnStateChangedDelegateInternal(void * jarg1, void * jarg2) {
  FDatasmithFacadeEndpointObserver *arg1 = (FDatasmithFacadeEndpointObserver *) 0 ;
  FDatasmithFacadeEndpointObserver::OnStateChangedDelegate arg2 = (FDatasmithFacadeEndpointObserver::OnStateChangedDelegate) 0 ;
  
  arg1 = (FDatasmithFacadeEndpointObserver *)jarg1; 
  arg2 = (FDatasmithFacadeEndpointObserver::OnStateChangedDelegate)jarg2; 
  (arg1)->UnregisterOnStateChangedDelegateInternal(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeEndpointObserver(void * jarg1) {
  FDatasmithFacadeEndpointObserver *arg1 = (FDatasmithFacadeEndpointObserver *) 0 ;
  
  arg1 = (FDatasmithFacadeEndpointObserver *)jarg1; 
  delete arg1;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeDirectLink_Init__SWIG_0() {
  unsigned int jresult ;
  bool result;
  
  result = (bool)FDatasmithFacadeDirectLink::Init();
  jresult = result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeDirectLink_Init__SWIG_1(unsigned int jarg1, wchar_t * jarg2) {
  unsigned int jresult ;
  bool arg1 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  bool result;
  
  arg1 = jarg1 ? true : false; 
  arg2 = (TCHAR *)jarg2; 
  result = (bool)FDatasmithFacadeDirectLink::Init(arg1,(wchar_t const *)arg2);
  jresult = result; 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeDirectLink_ValidateCommunicationSetup() {
  int jresult ;
  int result;
  
  result = (int)FDatasmithFacadeDirectLink::ValidateCommunicationSetup();
  jresult = result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeDirectLink_Shutdown() {
  unsigned int jresult ;
  bool result;
  
  result = (bool)FDatasmithFacadeDirectLink::Shutdown();
  jresult = result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeDirectLink_InitializeForScene(void * jarg1, void * jarg2) {
  unsigned int jresult ;
  FDatasmithFacadeDirectLink *arg1 = (FDatasmithFacadeDirectLink *) 0 ;
  FDatasmithFacadeScene *arg2 = (FDatasmithFacadeScene *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeDirectLink *)jarg1; 
  arg2 = (FDatasmithFacadeScene *)jarg2; 
  result = (bool)(arg1)->InitializeForScene(arg2);
  jresult = result; 
  return jresult;
}


SWIGEXPORT unsigned int SWIGSTDCALL CSharp_FDatasmithFacadeDirectLink_UpdateScene(void * jarg1, void * jarg2) {
  unsigned int jresult ;
  FDatasmithFacadeDirectLink *arg1 = (FDatasmithFacadeDirectLink *) 0 ;
  FDatasmithFacadeScene *arg2 = (FDatasmithFacadeScene *) 0 ;
  bool result;
  
  arg1 = (FDatasmithFacadeDirectLink *)jarg1; 
  arg2 = (FDatasmithFacadeScene *)jarg2; 
  result = (bool)(arg1)->UpdateScene(arg2);
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeDirectLink_CloseCurrentSource(void * jarg1) {
  FDatasmithFacadeDirectLink *arg1 = (FDatasmithFacadeDirectLink *) 0 ;
  
  arg1 = (FDatasmithFacadeDirectLink *)jarg1; 
  (arg1)->CloseCurrentSource();
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeDirectLink_AddEndpointObserver(void * jarg1, void * jarg2) {
  FDatasmithFacadeDirectLink *arg1 = (FDatasmithFacadeDirectLink *) 0 ;
  FDatasmithFacadeEndpointObserver *arg2 = (FDatasmithFacadeEndpointObserver *) 0 ;
  
  arg1 = (FDatasmithFacadeDirectLink *)jarg1; 
  arg2 = (FDatasmithFacadeEndpointObserver *)jarg2; 
  (arg1)->AddEndpointObserver(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeDirectLink_RemoveEndpointObserver(void * jarg1, void * jarg2) {
  FDatasmithFacadeDirectLink *arg1 = (FDatasmithFacadeDirectLink *) 0 ;
  FDatasmithFacadeEndpointObserver *arg2 = (FDatasmithFacadeEndpointObserver *) 0 ;
  
  arg1 = (FDatasmithFacadeDirectLink *)jarg1; 
  arg2 = (FDatasmithFacadeEndpointObserver *)jarg2; 
  (arg1)->RemoveEndpointObserver(arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeDirectLink() {
  void * jresult ;
  FDatasmithFacadeDirectLink *result = 0 ;
  
  result = (FDatasmithFacadeDirectLink *)new FDatasmithFacadeDirectLink();
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeDirectLink(void * jarg1) {
  FDatasmithFacadeDirectLink *arg1 = (FDatasmithFacadeDirectLink *) 0 ;
  
  arg1 = (FDatasmithFacadeDirectLink *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeUniqueNameProvider() {
  void * jresult ;
  FDatasmithFacadeUniqueNameProvider *result = 0 ;
  
  result = (FDatasmithFacadeUniqueNameProvider *)new FDatasmithFacadeUniqueNameProvider();
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeUniqueNameProvider_GenerateUniqueName(void * jarg1, wchar_t * jarg2) {
  wchar_t * jresult ;
  FDatasmithFacadeUniqueNameProvider *arg1 = (FDatasmithFacadeUniqueNameProvider *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeUniqueNameProvider *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  result = (TCHAR *)(arg1)->GenerateUniqueName((TCHAR const *)arg2);
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeUniqueNameProvider_Reserve(void * jarg1, int jarg2) {
  FDatasmithFacadeUniqueNameProvider *arg1 = (FDatasmithFacadeUniqueNameProvider *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeUniqueNameProvider *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->Reserve(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeUniqueNameProvider_AddExistingName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeUniqueNameProvider *arg1 = (FDatasmithFacadeUniqueNameProvider *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeUniqueNameProvider *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->AddExistingName((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeUniqueNameProvider_RemoveExistingName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeUniqueNameProvider *arg1 = (FDatasmithFacadeUniqueNameProvider *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeUniqueNameProvider *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->RemoveExistingName((TCHAR const *)arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeUniqueNameProvider(void * jarg1) {
  FDatasmithFacadeUniqueNameProvider *arg1 = (FDatasmithFacadeUniqueNameProvider *) 0 ;
  
  arg1 = (FDatasmithFacadeUniqueNameProvider *)jarg1; 
  delete arg1;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadePropertyCapture() {
  void * jresult ;
  FDatasmithFacadePropertyCapture *result = 0 ;
  
  result = (FDatasmithFacadePropertyCapture *)new FDatasmithFacadePropertyCapture();
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadePropertyCapture(void * jarg1) {
  FDatasmithFacadePropertyCapture *arg1 = (FDatasmithFacadePropertyCapture *) 0 ;
  
  arg1 = (FDatasmithFacadePropertyCapture *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadePropertyCapture_SetPropertyPath(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadePropertyCapture *arg1 = (FDatasmithFacadePropertyCapture *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadePropertyCapture *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetPropertyPath((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadePropertyCapture_GetPropertyPath(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadePropertyCapture *arg1 = (FDatasmithFacadePropertyCapture *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadePropertyCapture *)jarg1; 
  result = (TCHAR *)(arg1)->GetPropertyPath();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadePropertyCapture_GetCategory(void * jarg1) {
  int jresult ;
  FDatasmithFacadePropertyCapture *arg1 = (FDatasmithFacadePropertyCapture *) 0 ;
  EDatasmithFacadePropertyCategory result;
  
  arg1 = (FDatasmithFacadePropertyCapture *)jarg1; 
  result = (EDatasmithFacadePropertyCategory)((FDatasmithFacadePropertyCapture const *)arg1)->GetCategory();
  jresult = (int)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeActorBinding(void * jarg1) {
  void * jresult ;
  FDatasmithFacadeActor *arg1 = (FDatasmithFacadeActor *) 0 ;
  FDatasmithFacadeActorBinding *result = 0 ;
  
  arg1 = (FDatasmithFacadeActor *)jarg1; 
  result = (FDatasmithFacadeActorBinding *)new FDatasmithFacadeActorBinding(arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeActorBinding(void * jarg1) {
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_AddPropertyCapture(void * jarg1, void * jarg2) {
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  FDatasmithFacadePropertyCapture *arg2 = (FDatasmithFacadePropertyCapture *) 0 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  arg2 = (FDatasmithFacadePropertyCapture *)jarg2; 
  (arg1)->AddPropertyCapture(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_GetPropertyCapturesCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  result = (int32)((FDatasmithFacadeActorBinding const *)arg1)->GetPropertyCapturesCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_GetPropertyCapture(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  int32 arg2 ;
  FDatasmithFacadePropertyCapture *result = 0 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadePropertyCapture *)(arg1)->GetNewPropertyCapture(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_RemovePropertyCapture(void * jarg1, void * jarg2) {
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  FDatasmithFacadePropertyCapture *arg2 = (FDatasmithFacadePropertyCapture *) 0 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  arg2 = (FDatasmithFacadePropertyCapture *)jarg2; 
  (arg1)->RemovePropertyCapture(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_AddRelativeLocationCapture(void * jarg1, double jarg2, double jarg3, double jarg4) {
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  arg2 = (double)jarg2; 
  arg3 = (double)jarg3; 
  arg4 = (double)jarg4; 
  (arg1)->AddRelativeLocationCapture(arg2,arg3,arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_AddRelativeRotationCapture__SWIG_0(void * jarg1, double jarg2, double jarg3, double jarg4) {
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  arg2 = (double)jarg2; 
  arg3 = (double)jarg3; 
  arg4 = (double)jarg4; 
  (arg1)->AddRelativeRotationCapture(arg2,arg3,arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_AddRelativeRotationCapture__SWIG_1(void * jarg1, double jarg2, double jarg3, double jarg4, double jarg5) {
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  double arg5 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  arg2 = (double)jarg2; 
  arg3 = (double)jarg3; 
  arg4 = (double)jarg4; 
  arg5 = (double)jarg5; 
  (arg1)->AddRelativeRotationCapture(arg2,arg3,arg4,arg5);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_AddRelativeScaleCapture(void * jarg1, double jarg2, double jarg3, double jarg4) {
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  arg2 = (double)jarg2; 
  arg3 = (double)jarg3; 
  arg4 = (double)jarg4; 
  (arg1)->AddRelativeScaleCapture(arg2,arg3,arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_AddRelativeTransformCapture__SWIG_0(void * jarg1, double * jarg2, unsigned int jarg3) {
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  double *arg2 ;
  bool arg3 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  arg2 = (double *)jarg2; 
  arg3 = jarg3 ? true : false; 
  (arg1)->AddRelativeTransformCapture((double const (*))arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_AddRelativeTransformCapture__SWIG_1(void * jarg1, double * jarg2) {
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  double *arg2 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  arg2 = (double *)jarg2; 
  (arg1)->AddRelativeTransformCapture((double const (*))arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_AddRelativeTransformCapture__SWIG_2(void * jarg1, float * jarg2, unsigned int jarg3) {
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  float *arg2 ;
  bool arg3 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  arg2 = (float *)jarg2; 
  arg3 = jarg3 ? true : false; 
  (arg1)->AddRelativeTransformCapture((float const (*))arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_AddRelativeTransformCapture__SWIG_3(void * jarg1, float * jarg2) {
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  float *arg2 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  arg2 = (float *)jarg2; 
  (arg1)->AddRelativeTransformCapture((float const (*))arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_AddVisibilityCapture(void * jarg1, unsigned int jarg2) {
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  bool arg2 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  arg2 = jarg2 ? true : false; 
  (arg1)->AddVisibilityCapture(arg2);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_AddMaterialCapture(void * jarg1, void * jarg2) {
  FDatasmithFacadeActorBinding *arg1 = (FDatasmithFacadeActorBinding *) 0 ;
  FDatasmithFacadeBaseMaterial *arg2 = (FDatasmithFacadeBaseMaterial *) 0 ;
  
  arg1 = (FDatasmithFacadeActorBinding *)jarg1; 
  arg2 = (FDatasmithFacadeBaseMaterial *)jarg2; 
  (arg1)->AddMaterialCapture(arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeVariant(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeVariant *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeVariant *)new FDatasmithFacadeVariant((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeVariant(void * jarg1) {
  FDatasmithFacadeVariant *arg1 = (FDatasmithFacadeVariant *) 0 ;
  
  arg1 = (FDatasmithFacadeVariant *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeVariant_AddActorBinding(void * jarg1, void * jarg2) {
  FDatasmithFacadeVariant *arg1 = (FDatasmithFacadeVariant *) 0 ;
  FDatasmithFacadeActorBinding *arg2 = (FDatasmithFacadeActorBinding *) 0 ;
  
  arg1 = (FDatasmithFacadeVariant *)jarg1; 
  arg2 = (FDatasmithFacadeActorBinding *)jarg2; 
  (arg1)->AddActorBinding(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeVariant_GetActorBindingsCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeVariant *arg1 = (FDatasmithFacadeVariant *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeVariant *)jarg1; 
  result = (int32)((FDatasmithFacadeVariant const *)arg1)->GetActorBindingsCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeVariant_GetActorBinding(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeVariant *arg1 = (FDatasmithFacadeVariant *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeActorBinding *result = 0 ;
  
  arg1 = (FDatasmithFacadeVariant *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeActorBinding *)(arg1)->GetNewActorBinding(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeVariant_RemoveActorBinding(void * jarg1, void * jarg2) {
  FDatasmithFacadeVariant *arg1 = (FDatasmithFacadeVariant *) 0 ;
  FDatasmithFacadeActorBinding *arg2 = (FDatasmithFacadeActorBinding *) 0 ;
  
  arg1 = (FDatasmithFacadeVariant *)jarg1; 
  arg2 = (FDatasmithFacadeActorBinding *)jarg2; 
  (arg1)->RemoveActorBinding(arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeVariantSet(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeVariantSet *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeVariantSet *)new FDatasmithFacadeVariantSet((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeVariantSet(void * jarg1) {
  FDatasmithFacadeVariantSet *arg1 = (FDatasmithFacadeVariantSet *) 0 ;
  
  arg1 = (FDatasmithFacadeVariantSet *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeVariantSet_AddVariant(void * jarg1, void * jarg2) {
  FDatasmithFacadeVariantSet *arg1 = (FDatasmithFacadeVariantSet *) 0 ;
  FDatasmithFacadeVariant *arg2 = (FDatasmithFacadeVariant *) 0 ;
  
  arg1 = (FDatasmithFacadeVariantSet *)jarg1; 
  arg2 = (FDatasmithFacadeVariant *)jarg2; 
  (arg1)->AddVariant(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeVariantSet_GetVariantsCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeVariantSet *arg1 = (FDatasmithFacadeVariantSet *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeVariantSet *)jarg1; 
  result = (int32)((FDatasmithFacadeVariantSet const *)arg1)->GetVariantsCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeVariantSet_GetVariant(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeVariantSet *arg1 = (FDatasmithFacadeVariantSet *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeVariant *result = 0 ;
  
  arg1 = (FDatasmithFacadeVariantSet *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeVariant *)(arg1)->GetNewVariant(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeVariantSet_RemoveVariant(void * jarg1, void * jarg2) {
  FDatasmithFacadeVariantSet *arg1 = (FDatasmithFacadeVariantSet *) 0 ;
  FDatasmithFacadeVariant *arg2 = (FDatasmithFacadeVariant *) 0 ;
  
  arg1 = (FDatasmithFacadeVariantSet *)jarg1; 
  arg2 = (FDatasmithFacadeVariant *)jarg2; 
  (arg1)->RemoveVariant(arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeLevelVariantSets(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeLevelVariantSets *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeLevelVariantSets *)new FDatasmithFacadeLevelVariantSets((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeLevelVariantSets(void * jarg1) {
  FDatasmithFacadeLevelVariantSets *arg1 = (FDatasmithFacadeLevelVariantSets *) 0 ;
  
  arg1 = (FDatasmithFacadeLevelVariantSets *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeLevelVariantSets_AddVariantSet(void * jarg1, void * jarg2) {
  FDatasmithFacadeLevelVariantSets *arg1 = (FDatasmithFacadeLevelVariantSets *) 0 ;
  FDatasmithFacadeVariantSet *arg2 = (FDatasmithFacadeVariantSet *) 0 ;
  
  arg1 = (FDatasmithFacadeLevelVariantSets *)jarg1; 
  arg2 = (FDatasmithFacadeVariantSet *)jarg2; 
  (arg1)->AddVariantSet(arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeLevelVariantSets_GetVariantSetsCount(void * jarg1) {
  int jresult ;
  FDatasmithFacadeLevelVariantSets *arg1 = (FDatasmithFacadeLevelVariantSets *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeLevelVariantSets *)jarg1; 
  result = (int32)((FDatasmithFacadeLevelVariantSets const *)arg1)->GetVariantSetsCount();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_FDatasmithFacadeLevelVariantSets_GetVariantSet(void * jarg1, int jarg2) {
  void * jresult ;
  FDatasmithFacadeLevelVariantSets *arg1 = (FDatasmithFacadeLevelVariantSets *) 0 ;
  int32 arg2 ;
  FDatasmithFacadeVariantSet *result = 0 ;
  
  arg1 = (FDatasmithFacadeLevelVariantSets *)jarg1; 
  arg2 = (int32)jarg2; 
  result = (FDatasmithFacadeVariantSet *)(arg1)->GetNewVariantSet(arg2);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeLevelVariantSets_RemoveVariantSet(void * jarg1, void * jarg2) {
  FDatasmithFacadeLevelVariantSets *arg1 = (FDatasmithFacadeLevelVariantSets *) 0 ;
  FDatasmithFacadeVariantSet *arg2 = (FDatasmithFacadeVariantSet *) 0 ;
  
  arg1 = (FDatasmithFacadeLevelVariantSets *)jarg1; 
  arg2 = (FDatasmithFacadeVariantSet *)jarg2; 
  (arg1)->RemoveVariantSet(arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeActorDecal(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeActorDecal *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeActorDecal *)new FDatasmithFacadeActorDecal((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeActorDecal(void * jarg1) {
  FDatasmithFacadeActorDecal *arg1 = (FDatasmithFacadeActorDecal *) 0 ;
  
  arg1 = (FDatasmithFacadeActorDecal *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorDecal_GetDimensions(void * jarg1, double * jarg2, double * jarg3, double * jarg4) {
  FDatasmithFacadeActorDecal *arg1 = (FDatasmithFacadeActorDecal *) 0 ;
  double *arg2 = 0 ;
  double *arg3 = 0 ;
  double *arg4 = 0 ;
  
  arg1 = (FDatasmithFacadeActorDecal *)jarg1; 
  arg2 = (double *)jarg2; 
  arg3 = (double *)jarg3; 
  arg4 = (double *)jarg4; 
  ((FDatasmithFacadeActorDecal const *)arg1)->GetDimensions(*arg2,*arg3,*arg4);
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorDecal_SetDimensions(void * jarg1, double jarg2, double jarg3, double jarg4) {
  FDatasmithFacadeActorDecal *arg1 = (FDatasmithFacadeActorDecal *) 0 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  
  arg1 = (FDatasmithFacadeActorDecal *)jarg1; 
  arg2 = (double)jarg2; 
  arg3 = (double)jarg3; 
  arg4 = (double)jarg4; 
  (arg1)->SetDimensions(arg2,arg3,arg4);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeActorDecal_GetDecalMaterialPathName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeActorDecal *arg1 = (FDatasmithFacadeActorDecal *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeActorDecal *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeActorDecal const *)arg1)->GetDecalMaterialPathName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorDecal_SetDecalMaterialPathName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeActorDecal *arg1 = (FDatasmithFacadeActorDecal *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeActorDecal *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetDecalMaterialPathName((TCHAR const *)arg2);
}


SWIGEXPORT int SWIGSTDCALL CSharp_FDatasmithFacadeActorDecal_GetSortOrder(void * jarg1) {
  int jresult ;
  FDatasmithFacadeActorDecal *arg1 = (FDatasmithFacadeActorDecal *) 0 ;
  int32 result;
  
  arg1 = (FDatasmithFacadeActorDecal *)jarg1; 
  result = (int32)((FDatasmithFacadeActorDecal const *)arg1)->GetSortOrder();
  jresult = result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeActorDecal_SetSortOrder(void * jarg1, int jarg2) {
  FDatasmithFacadeActorDecal *arg1 = (FDatasmithFacadeActorDecal *) 0 ;
  int32 arg2 ;
  
  arg1 = (FDatasmithFacadeActorDecal *)jarg1; 
  arg2 = (int32)jarg2; 
  (arg1)->SetSortOrder(arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_new_FDatasmithFacadeDecalMaterial(wchar_t * jarg1) {
  void * jresult ;
  TCHAR *arg1 = (TCHAR *) 0 ;
  FDatasmithFacadeDecalMaterial *result = 0 ;
  
  arg1 = (TCHAR *)jarg1; 
  result = (FDatasmithFacadeDecalMaterial *)new FDatasmithFacadeDecalMaterial((TCHAR const *)arg1);
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_FDatasmithFacadeDecalMaterial(void * jarg1) {
  FDatasmithFacadeDecalMaterial *arg1 = (FDatasmithFacadeDecalMaterial *) 0 ;
  
  arg1 = (FDatasmithFacadeDecalMaterial *)jarg1; 
  delete arg1;
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeDecalMaterial_GetDiffuseTexturePathName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeDecalMaterial *arg1 = (FDatasmithFacadeDecalMaterial *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeDecalMaterial *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeDecalMaterial const *)arg1)->GetDiffuseTexturePathName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeDecalMaterial_SetDiffuseTexturePathName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeDecalMaterial *arg1 = (FDatasmithFacadeDecalMaterial *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeDecalMaterial *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetDiffuseTexturePathName((TCHAR const *)arg2);
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_FDatasmithFacadeDecalMaterial_GetNormalTexturePathName(void * jarg1) {
  wchar_t * jresult ;
  FDatasmithFacadeDecalMaterial *arg1 = (FDatasmithFacadeDecalMaterial *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (FDatasmithFacadeDecalMaterial *)jarg1; 
  result = (TCHAR *)((FDatasmithFacadeDecalMaterial const *)arg1)->GetNormalTexturePathName();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_FDatasmithFacadeDecalMaterial_SetNormalTexturePathName(void * jarg1, wchar_t * jarg2) {
  FDatasmithFacadeDecalMaterial *arg1 = (FDatasmithFacadeDecalMaterial *) 0 ;
  TCHAR *arg2 = (TCHAR *) 0 ;
  
  arg1 = (FDatasmithFacadeDecalMaterial *)jarg1; 
  arg2 = (TCHAR *)jarg2; 
  (arg1)->SetNormalTexturePathName((TCHAR const *)arg2);
}


SWIGEXPORT void * SWIGSTDCALL CSharp_IDatasmithExporterUIModule_Get() {
  void * jresult ;
  IDatasmithExporterUIModule *result = 0 ;
  
  result = (IDatasmithExporterUIModule *)IDatasmithExporterUIModule::Get();
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void * SWIGSTDCALL CSharp_IDatasmithExporterUIModule_GetDirectLinkExporterUI(void * jarg1) {
  void * jresult ;
  IDatasmithExporterUIModule *arg1 = (IDatasmithExporterUIModule *) 0 ;
  IDirectLinkUI *result = 0 ;
  
  arg1 = (IDatasmithExporterUIModule *)jarg1; 
  result = (IDirectLinkUI *)((IDatasmithExporterUIModule const *)arg1)->GetDirectLinkExporterUI();
  jresult = (void *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_IDatasmithExporterUIModule(void * jarg1) {
  IDatasmithExporterUIModule *arg1 = (IDatasmithExporterUIModule *) 0 ;
  
  arg1 = (IDatasmithExporterUIModule *)jarg1; 
  delete arg1;
}


SWIGEXPORT void SWIGSTDCALL CSharp_IDirectLinkUI_SetStreamWindowCenter(void * jarg1, int jarg2, int jarg3) {
  IDirectLinkUI *arg1 = (IDirectLinkUI *) 0 ;
  int arg2 ;
  int arg3 ;
  
  arg1 = (IDirectLinkUI *)jarg1; 
  arg2 = (int)jarg2; 
  arg3 = (int)jarg3; 
  (arg1)->SetStreamWindowCenter(arg2,arg3);
}


SWIGEXPORT void SWIGSTDCALL CSharp_IDirectLinkUI_OpenDirectLinkStreamWindow(void * jarg1) {
  IDirectLinkUI *arg1 = (IDirectLinkUI *) 0 ;
  
  arg1 = (IDirectLinkUI *)jarg1; 
  (arg1)->OpenDirectLinkStreamWindow();
}


SWIGEXPORT wchar_t * SWIGSTDCALL CSharp_IDirectLinkUI_GetDirectLinkCacheDirectory(void * jarg1) {
  wchar_t * jresult ;
  IDirectLinkUI *arg1 = (IDirectLinkUI *) 0 ;
  TCHAR *result = 0 ;
  
  arg1 = (IDirectLinkUI *)jarg1; 
  result = (TCHAR *)(arg1)->GetDirectLinkCacheDirectory();
  jresult = (wchar_t *)result; 
  return jresult;
}


SWIGEXPORT void SWIGSTDCALL CSharp_delete_IDirectLinkUI(void * jarg1) {
  IDirectLinkUI *arg1 = (IDirectLinkUI *) 0 ;
  
  arg1 = (IDirectLinkUI *)jarg1; 
  delete arg1;
}


SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadeKeyValueProperty_SWIGUpcast(FDatasmithFacadeKeyValueProperty *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadeTexture_SWIGUpcast(FDatasmithFacadeTexture *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadeBaseMaterial_SWIGUpcast(FDatasmithFacadeBaseMaterial *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeBaseMaterial * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialInstance_SWIGUpcast(FDatasmithFacadeMaterialInstance *jarg1) {
    return (FDatasmithFacadeBaseMaterial *)jarg1;
}

SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialID_SWIGUpcast(FDatasmithFacadeMaterialID *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeExpressionParameter * SWIGSTDCALL CSharp_FDatasmithFacadeExpressionParameterSwigImpl_FDatasmithFacadeExpressionParameter_GetInterfaceCPtr(FDatasmithFacadeExpressionParameter *jarg1) {
    return (FDatasmithFacadeExpressionParameter *)jarg1;
}

SWIGEXPORT FDatasmithFacadeExpressionParameter * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionBool_FDatasmithFacadeExpressionParameter_GetInterfaceCPtr(FDatasmithFacadeMaterialExpressionBool *jarg1) {
    return (FDatasmithFacadeExpressionParameter *)jarg1;
}

SWIGEXPORT FDatasmithFacadeMaterialExpression * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionBool_SWIGUpcast(FDatasmithFacadeMaterialExpressionBool *jarg1) {
    return (FDatasmithFacadeMaterialExpression *)jarg1;
}

SWIGEXPORT FDatasmithFacadeExpressionParameter * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionColor_FDatasmithFacadeExpressionParameter_GetInterfaceCPtr(FDatasmithFacadeMaterialExpressionColor *jarg1) {
    return (FDatasmithFacadeExpressionParameter *)jarg1;
}

SWIGEXPORT FDatasmithFacadeMaterialExpression * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionColor_SWIGUpcast(FDatasmithFacadeMaterialExpressionColor *jarg1) {
    return (FDatasmithFacadeMaterialExpression *)jarg1;
}

SWIGEXPORT FDatasmithFacadeExpressionParameter * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionScalar_FDatasmithFacadeExpressionParameter_GetInterfaceCPtr(FDatasmithFacadeMaterialExpressionScalar *jarg1) {
    return (FDatasmithFacadeExpressionParameter *)jarg1;
}

SWIGEXPORT FDatasmithFacadeMaterialExpression * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionScalar_SWIGUpcast(FDatasmithFacadeMaterialExpressionScalar *jarg1) {
    return (FDatasmithFacadeMaterialExpression *)jarg1;
}

SWIGEXPORT FDatasmithFacadeExpressionParameter * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTexture_FDatasmithFacadeExpressionParameter_GetInterfaceCPtr(FDatasmithFacadeMaterialExpressionTexture *jarg1) {
    return (FDatasmithFacadeExpressionParameter *)jarg1;
}

SWIGEXPORT FDatasmithFacadeMaterialExpression * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTexture_SWIGUpcast(FDatasmithFacadeMaterialExpressionTexture *jarg1) {
    return (FDatasmithFacadeMaterialExpression *)jarg1;
}

SWIGEXPORT FDatasmithFacadeMaterialExpression * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionTextureCoordinate_SWIGUpcast(FDatasmithFacadeMaterialExpressionTextureCoordinate *jarg1) {
    return (FDatasmithFacadeMaterialExpression *)jarg1;
}

SWIGEXPORT FDatasmithFacadeMaterialExpression * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionFlattenNormal_SWIGUpcast(FDatasmithFacadeMaterialExpressionFlattenNormal *jarg1) {
    return (FDatasmithFacadeMaterialExpression *)jarg1;
}

SWIGEXPORT FDatasmithFacadeMaterialExpression * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionGeneric_SWIGUpcast(FDatasmithFacadeMaterialExpressionGeneric *jarg1) {
    return (FDatasmithFacadeMaterialExpression *)jarg1;
}

SWIGEXPORT FDatasmithFacadeMaterialExpression * SWIGSTDCALL CSharp_FDatasmithFacadeMaterialExpressionFunctionCall_SWIGUpcast(FDatasmithFacadeMaterialExpressionFunctionCall *jarg1) {
    return (FDatasmithFacadeMaterialExpression *)jarg1;
}

SWIGEXPORT FDatasmithFacadeBaseMaterial * SWIGSTDCALL CSharp_FDatasmithFacadeUEPbrMaterial_SWIGUpcast(FDatasmithFacadeUEPbrMaterial *jarg1) {
    return (FDatasmithFacadeBaseMaterial *)jarg1;
}

SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadeMeshElement_SWIGUpcast(FDatasmithFacadeMeshElement *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadeMetaData_SWIGUpcast(FDatasmithFacadeMetaData *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadeActor_SWIGUpcast(FDatasmithFacadeActor *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeActor * SWIGSTDCALL CSharp_FDatasmithFacadeActorCamera_SWIGUpcast(FDatasmithFacadeActorCamera *jarg1) {
    return (FDatasmithFacadeActor *)jarg1;
}

SWIGEXPORT FDatasmithFacadeActor * SWIGSTDCALL CSharp_FDatasmithFacadeActorLight_SWIGUpcast(FDatasmithFacadeActorLight *jarg1) {
    return (FDatasmithFacadeActor *)jarg1;
}

SWIGEXPORT FDatasmithFacadeActorLight * SWIGSTDCALL CSharp_FDatasmithFacadePointLight_SWIGUpcast(FDatasmithFacadePointLight *jarg1) {
    return (FDatasmithFacadeActorLight *)jarg1;
}

SWIGEXPORT FDatasmithFacadePointLight * SWIGSTDCALL CSharp_FDatasmithFacadeSpotLight_SWIGUpcast(FDatasmithFacadeSpotLight *jarg1) {
    return (FDatasmithFacadePointLight *)jarg1;
}

SWIGEXPORT FDatasmithFacadeActorLight * SWIGSTDCALL CSharp_FDatasmithFacadeDirectionalLight_SWIGUpcast(FDatasmithFacadeDirectionalLight *jarg1) {
    return (FDatasmithFacadeActorLight *)jarg1;
}

SWIGEXPORT FDatasmithFacadeSpotLight * SWIGSTDCALL CSharp_FDatasmithFacadeAreaLight_SWIGUpcast(FDatasmithFacadeAreaLight *jarg1) {
    return (FDatasmithFacadeSpotLight *)jarg1;
}

SWIGEXPORT FDatasmithFacadePointLight * SWIGSTDCALL CSharp_FDatasmithFacadeLightmassPortal_SWIGUpcast(FDatasmithFacadeLightmassPortal *jarg1) {
    return (FDatasmithFacadePointLight *)jarg1;
}

SWIGEXPORT FDatasmithFacadeActor * SWIGSTDCALL CSharp_FDatasmithFacadeActorMesh_SWIGUpcast(FDatasmithFacadeActorMesh *jarg1) {
    return (FDatasmithFacadeActor *)jarg1;
}

SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadeBaseAnimation_SWIGUpcast(FDatasmithFacadeBaseAnimation *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeBaseAnimation * SWIGSTDCALL CSharp_FDatasmithFacadeTransformAnimation_SWIGUpcast(FDatasmithFacadeTransformAnimation *jarg1) {
    return (FDatasmithFacadeBaseAnimation *)jarg1;
}

SWIGEXPORT FDatasmithFacadeBaseAnimation * SWIGSTDCALL CSharp_FDatasmithFacadeVisibilityAnimation_SWIGUpcast(FDatasmithFacadeVisibilityAnimation *jarg1) {
    return (FDatasmithFacadeBaseAnimation *)jarg1;
}

SWIGEXPORT FDatasmithFacadeBaseAnimation * SWIGSTDCALL CSharp_FDatasmithFacadeSubsequenceAnimation_SWIGUpcast(FDatasmithFacadeSubsequenceAnimation *jarg1) {
    return (FDatasmithFacadeBaseAnimation *)jarg1;
}

SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadeLevelSequence_SWIGUpcast(FDatasmithFacadeLevelSequence *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadePropertyCapture_SWIGUpcast(FDatasmithFacadePropertyCapture *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadeActorBinding_SWIGUpcast(FDatasmithFacadeActorBinding *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadeVariant_SWIGUpcast(FDatasmithFacadeVariant *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadeVariantSet_SWIGUpcast(FDatasmithFacadeVariantSet *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeElement * SWIGSTDCALL CSharp_FDatasmithFacadeLevelVariantSets_SWIGUpcast(FDatasmithFacadeLevelVariantSets *jarg1) {
    return (FDatasmithFacadeElement *)jarg1;
}

SWIGEXPORT FDatasmithFacadeActor * SWIGSTDCALL CSharp_FDatasmithFacadeActorDecal_SWIGUpcast(FDatasmithFacadeActorDecal *jarg1) {
    return (FDatasmithFacadeActor *)jarg1;
}

SWIGEXPORT FDatasmithFacadeBaseMaterial * SWIGSTDCALL CSharp_FDatasmithFacadeDecalMaterial_SWIGUpcast(FDatasmithFacadeDecalMaterial *jarg1) {
    return (FDatasmithFacadeBaseMaterial *)jarg1;
}

#ifdef __cplusplus
}
#endif

