<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>NVIDIA Video Codec SDK</Name>
  <Location>Engine/Source/ThirdParty/NVIDIA/NvEncode</Location>
  <Function>NVIDIA Video Codec SDK contain the NVENC API, which will allow us to use hardware accelerated video encoding for Pixel Streaming.</Function>
  <Eula>https://developer.download.nvidia.com/designworks/DesignWorks_SDKs_Samples_Tools_License_distrib_use_rights_2017_06_13.pdf?Mt9P5gDcw8_xf4f4Bu4F-1sZpdCV8uOtqHRneXvQLp1Kla0zCVSMPkTSQkk9eyk5TrfZBbbhwsWIFVQjkpzPujUUsbzYovK1pSAAMOiV3twj7d9xILgbGB0qyFv1jI-9XU4a6HfQJICku7UdgY22q_FekPqr_gVMf2x5UqtBeZkdYxKz1U_xm8iGbMExGW7s97LX</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>