/*
 * Copyright (c) 2023 Epic Games, Inc. All Rights Reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY EPIC GAMES, INC. ``AS IS'' AND ANY
 * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF ME<PERSON><PERSON><PERSON><PERSON><PERSON>ITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL EPIC GAMES, INC. OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 * PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 * OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. 
 */

#include "pas_config.h"

#if LIBPAS_ENABLED

#include "inline_non_committable_granules_heap.h"

#if PAS_ENABLE_INLINE_NON_COMMITTABLE_GRANULES

#include "inline_non_committable_granules_config.h"
#include "pas_deallocate.h"
#include "pas_try_allocate_intrinsic.h"

pas_intrinsic_heap_support inline_non_committable_granules_common_primitive_heap_support =
    PAS_INTRINSIC_HEAP_SUPPORT_INITIALIZER;

pas_heap inline_non_committable_granules_common_primitive_heap =
    PAS_INTRINSIC_HEAP_INITIALIZER(
        &inline_non_committable_granules_common_primitive_heap,
        PAS_SIMPLE_TYPE_CREATE(1, 1),
        inline_non_committable_granules_common_primitive_heap_support,
        INLINE_NON_COMMITTABLE_GRANULES_CONFIG,
        &inline_non_committable_granules_runtime_config);

pas_allocator_counts inline_non_committable_granules_allocator_counts;

PAS_CREATE_TRY_ALLOCATE_INTRINSIC(
    test_allocate_common_primitive,
    INLINE_NON_COMMITTABLE_GRANULES_CONFIG,
    &inline_non_committable_granules_runtime_config,
    &inline_non_committable_granules_allocator_counts,
    pas_allocation_result_crash_on_error,
    &inline_non_committable_granules_common_primitive_heap,
    &inline_non_committable_granules_common_primitive_heap_support,
    pas_intrinsic_heap_is_not_designated);

void* inline_non_committable_granules_allocate(size_t size)
{
    return (void*)test_allocate_common_primitive(size, 1).begin;
}

void inline_non_committable_granules_deallocate(void* ptr)
{
    pas_deallocate(ptr, INLINE_NON_COMMITTABLE_GRANULES_CONFIG);
}

#endif /* PAS_ENABLE_INLINE_NON_COMMITTABLE_GRANULES */

#endif /* LIBPAS_ENABLED */

