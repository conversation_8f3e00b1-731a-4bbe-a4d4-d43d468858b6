<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Eigen</Name>
  <Location>/Engine/Source/ThirdParty/Eigen/</Location>
  <Function> Eigen is a C++ template library for linear algebra: matrices, vectors, numerical solvers, and related algorithms.</Function>
  <Eula>http://eigen.tuxfamily.org/index.php?title=Main_Page#License</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>