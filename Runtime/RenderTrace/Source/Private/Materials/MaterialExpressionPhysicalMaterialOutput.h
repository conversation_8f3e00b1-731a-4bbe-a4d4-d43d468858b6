// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "MaterialExpressionIO.h"
#include "Materials/MaterialExpressionCustomOutput.h"
#include "MaterialValueType.h"

#include "MaterialExpressionPhysicalMaterialOutput.generated.h"

class UPhysicalMaterial;

/** Structure linking a material expression input with a physical material. For use by UMaterialExpressionPhysicalMaterialOutput. */
USTRUCT()
struct FPhysicalMaterialTraceInput
{
	GENERATED_USTRUCT_BODY()

	UPROPERTY(EditAnywhere, Category = PhysicalMaterial)
	TObjectPtr<UPhysicalMaterial> PhysicalMaterial;

	UPROPERTY(meta = (RequiredInput = "true"))
	FExpressionInput Input;

	FPhysicalMaterialTraceInput()
		: PhysicalMaterial(nullptr)
	{}
};

/** 
 * Custom output node to write out physical material weights.
 */
UCLASS(collapsecategories, hidecategories=Object)
class RENDERTRACE_API UMaterialExpressionPhysicalMaterialOutput : public UMaterialExpressionCustomOutput
{
	GENERATED_UCLASS_BODY()

	/** Array of physical material inputs. */
	UPROPERTY(EditAnywhere, Category = PhysicalMaterial)
	TArray<FPhysicalMaterialTraceInput> Inputs;

#if WITH_EDITOR
	const UPhysicalMaterial* GetInputMaterialFromMap(int32 Index) const;
	void FillMaterialNames();

	//~ Begin UObject Interface
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
	virtual void PostLoad() override;
	//~ End UObject Interface

	//~ Begin UMaterialExpression Interface
	virtual void GetCaption(TArray<FString>& OutCaptions) const override;
	virtual TArrayView<FExpressionInput*> GetInputsView() override;
	virtual FExpressionInput* GetInput(int32 InputIndex) override;
	virtual FName GetInputName(int32 InputIndex) const override;
	virtual uint32 GetInputType(int32 InputIndex) override { return MCT_Float; }
	virtual int32 Compile(class FMaterialCompiler* Compiler, int32 OutputIndex) override;
	//~ End UMaterialExpression Interface
#endif

	//~ Begin UMaterialExpressionCustomOutput Interface
	virtual int32 GetNumOutputs() const override;
	virtual FString GetFunctionName() const override { return TEXT("GetRenderTracePhysicalMaterial"); }
	//~ End UMaterialExpressionCustomOutput Interface
};
