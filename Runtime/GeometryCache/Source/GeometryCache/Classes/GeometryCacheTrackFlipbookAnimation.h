// Copyright Epic Games, Inc. All Rights Reserved.
#pragma once

#include "GeometryCacheTrack.h"

#include "GeometryCacheTrackFlipbookAnimation.generated.h"

/** Derived GeometryCacheTrack class, used for Transform animation. */
UCLASS(collapsecategories, hidecategories = Object, BlueprintType, config = Engine, deprecated)
class GEOMETRYCACHE_API UDEPRECATED_GeometryCacheTrack_FlipbookAnimation : public UGeometryCacheTrack
{
	GENERATED_UCLASS_BODY()

	virtual ~UDEPRECATED_GeometryCacheTrack_FlipbookAnimation();

	//~ Begin UObject Interface.
	virtual void GetResourceSizeEx(FResourceSizeEx& CumulativeResourceSize) override;
	virtual void BeginDestroy() override;
	//~ End UObject Interface.

	//~ Begin UGeometryCacheTrack Interface.
	virtual const bool UpdateMeshData(const float Time, const bool bLooping, int32& InOutMeshSampleIndex, FGeometryCacheMeshData*& OutMeshData) override;
	virtual const float GetMaxSampleTime() const override;
	//~ End UGeometryCacheTrack Interface.

	/**
	* Add a GeometryCacheMeshData sample to the Track
	*
	* @param MeshData - Holds the mesh data for the specific sample
	* @param SampleTime - SampleTime for the specific sample being added
	* @return void
	*/
	UFUNCTION()
	void AddMeshSample(const FGeometryCacheMeshData& MeshData, const float SampleTime);

private:
	/** Number of Mesh Sample within this track */
	UPROPERTY(VisibleAnywhere, Category = GeometryCache)
	uint32 NumMeshSamples;

	/** Stored data for each Mesh sample */
	TArray<FGeometryCacheMeshData> MeshSamples;
	TArray<float> MeshSampleTimes;
};


#if UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2
#include "CoreMinimal.h"
#include "GeometryCacheMeshData.h"
#endif
