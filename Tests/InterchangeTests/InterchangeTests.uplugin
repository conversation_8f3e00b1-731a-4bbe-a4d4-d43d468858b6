{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Interchange Tests", "Description": "Plugin for Interchange automation tests.", "Category": "Testing", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": true, "CanContainContent": false, "IsBetaVersion": true, "IsExperimentalVersion": false, "Installed": false, "Modules": [{"Name": "InterchangeTests", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "InterchangeTestEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "Interchange", "Enabled": true, "TargetAllowList": ["Editor", "Program"]}, {"Name": "VariantManager<PERSON><PERSON>nt", "Enabled": true}]}