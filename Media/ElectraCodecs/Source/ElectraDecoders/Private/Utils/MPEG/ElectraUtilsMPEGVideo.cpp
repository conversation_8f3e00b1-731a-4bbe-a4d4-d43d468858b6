// Copyright Epic Games, Inc. All Rights Reserved.

#include "Utils/MPEG/ElectraUtilsMPEGVideo.h"
#include "Utils/ElectraBitstreamReader.h"
#include "Utils/MPEG/ElectraUtilsMPEGVideo_H264.h"
#include "Utils/MPEG/ElectraUtilsMPEGVideo_H265.h"
#include "ElectraDecodersUtils.h"

namespace ElectraDecodersUtil
{
	namespace MPEG
	{
		void FAVCDecoderConfigurationRecord::SetRawData(const void* Data, int64 Size)
		{
			RawData.Empty();
			if (Size)
			{
				RawData.Reserve((uint32)Size);
				RawData.SetNumUninitialized((uint32)Size);
				FMemory::Memcpy(RawData.GetData(), Data, Size);
			}
		}

		const TArray<uint8>& FAVCDecoderConfigurationRecord::GetRawData() const
		{
			return RawData;
		}

		const TArray<uint8>& FAVCDecoderConfigurationRecord::GetCodecSpecificData() const
		{
			return CodecSpecificData;
		}

		const TArray<uint8>& FAVCDecoderConfigurationRecord::GetCodecSpecificDataSPS() const
		{
			return CodecSpecificDataSPSOnly;
		}

		const TArray<uint8>& FAVCDecoderConfigurationRecord::GetCodecSpecificDataPPS() const
		{
			return CodecSpecificDataPPSOnly;
		}

		int32 FAVCDecoderConfigurationRecord::GetNumberOfSPS() const
		{
			return ParsedSPSs.Num();
		}

		const FISO14496_10_seq_parameter_set_data& FAVCDecoderConfigurationRecord::GetParsedSPS(int32 SpsIndex) const
		{
			check(SpsIndex < GetNumberOfSPS());
			return ParsedSPSs[SpsIndex];
		}


		bool FAVCDecoderConfigurationRecord::CreateFromCodecSpecificData(const TArray<uint8>& InFromCSD)
		{
			if (!InFromCSD.IsEmpty())
			{
				CodecSpecificData = InFromCSD;
				TArray<ElectraDecodersUtil::MPEG::H264::FNaluInfo> Nalus;
				if (!ElectraDecodersUtil::MPEG::H264::ParseBitstreamForNALUs(Nalus, InFromCSD.GetData(), InFromCSD.Num()))
				{
					return false;
				}

				FArray aSPS, aPPS;
				for(int32 i=0; i<Nalus.Num(); ++i)
				{
					if (Nalus[i].Type == 7)
					{
						TMap<uint32, ElectraDecodersUtil::MPEG::H264::FSequenceParameterSet> SPSs;
						if (ElectraDecodersUtil::MPEG::H264::ParseSequenceParameterSet(SPSs, InFromCSD.GetData() + Nalus[i].Offset + Nalus[i].UnitLength, Nalus[i].Size))
						{
							ElectraDecodersUtil::MPEG::H264::FSequenceParameterSet sps(SPSs.CreateConstIterator().Value());
							AVCProfileIndication = sps.profile_idc;
							ProfileCompatibility = (sps.constraint_set0_flag << 7) | (sps.constraint_set1_flag << 6) | (sps.constraint_set2_flag << 5) |
												   (sps.constraint_set3_flag << 4) | (sps.constraint_set4_flag << 3) | (sps.constraint_set5_flag << 2);
							AVCLevelIndication = sps.level_idc;
							NALUnitLength = 4;
							ChromaFormat = sps.chroma_format_idc;
							BitDepthLumaMinus8 = sps.bit_depth_luma_minus8;
							BitDepthChromaMinus8 = sps.bit_depth_chroma_minus8;
							bHaveAdditionalProfileIndication = false;
						}
						else
						{
							return false;
						}
						aSPS.NALUs.Emplace_GetRef() = MakeArrayView<const uint8>(InFromCSD.GetData() + Nalus[i].Offset + Nalus[i].UnitLength, Nalus[i].Size);
					}
					else if (Nalus[i].Type == 8)
					{
						aPPS.NALUs.Emplace_GetRef() = MakeArrayView<const uint8>(InFromCSD.GetData() + Nalus[i].Offset + Nalus[i].UnitLength, Nalus[i].Size);
					}
				}
				if (aSPS.NALUs.IsEmpty() || aPPS.NALUs.IsEmpty())
				{
					return false;
				}

				// Create the raw configuration record now.
				ConfigurationVersion = 1;
				FElectraBitstreamWriter wr;
				wr.PutBits(ConfigurationVersion, 8);
				wr.PutBits(AVCProfileIndication, 8);
				wr.PutBits(ProfileCompatibility, 8);
				wr.PutBits(AVCLevelIndication, 8);
				wr.PutBits(63U, 6);
				wr.PutBits(NALUnitLength-1, 2);
				wr.PutBits(7U, 3);
				wr.PutBits(static_cast<uint32>(aSPS.NALUs.Num()), 5);
				for(int32 j=0; j<aSPS.NALUs.Num(); ++j)
				{
					const TArray<uint8>& d(aSPS.NALUs[j]);
					wr.PutBits(static_cast<uint32>(d.Num()), 16);
					for(int32 k=0; k<d.Num(); ++k)
					{
						wr.PutBits(d[k], 8);
					}
				}
				wr.PutBits(static_cast<uint32>(aPPS.NALUs.Num()), 8);
				for(int32 j=0; j<aPPS.NALUs.Num(); ++j)
				{
					const TArray<uint8>& d(aPPS.NALUs[j]);
					wr.PutBits(static_cast<uint32>(d.Num()), 16);
					for(int32 k=0; k<d.Num(); ++k)
					{
						wr.PutBits(d[k], 8);
					}
				}
				if (AVCProfileIndication != 66 && AVCProfileIndication != 77 && AVCProfileIndication != 88)
				{
					wr.PutBits(63U, 6);
					wr.PutBits(ChromaFormat, 2);
					wr.PutBits(31U, 5);
					wr.PutBits(BitDepthLumaMinus8, 3);
					wr.PutBits(31U, 5);
					wr.PutBits(BitDepthChromaMinus8, 3);
					wr.PutBits(0U, 8);
				}
				wr.GetArray(RawData);
				return true;
			}
			return false;
		}

		bool FAVCDecoderConfigurationRecord::Parse()
		{
			CodecSpecificData.Empty();

			if (RawData.Num())
			{
				FElectraByteReader ByteReader(RawData.GetData(), RawData.Num());

				if (!ByteReader.ReadByte(ConfigurationVersion))
				{
					return false;
				}
				if (!ByteReader.ReadByte(AVCProfileIndication))
				{
					return false;
				}
				if (!ByteReader.ReadByte(ProfileCompatibility))
				{
					return false;
				}
				if (!ByteReader.ReadByte(AVCLevelIndication))
				{
					return false;
				}
				if (!ByteReader.ReadByte(NALUnitLength))
				{
					return false;
				}
				NALUnitLength = (NALUnitLength & 3) + 1;
				uint8 nSPS = 0;
				if (!ByteReader.ReadByte(nSPS))
				{
					return false;
				}
				nSPS &= 31;
				if (nSPS)
				{
					SequenceParameterSets.Reserve(nSPS);
					ParsedSPSs.Reserve(nSPS);
				}
				int32 TotalSPSSize = 0;
				for(int32 i = 0; i < nSPS; ++i)
				{
					uint16 spsLen = 0;
					if (!ByteReader.ReadByte(spsLen))
					{
						return false;
					}
					TArray<uint8>& sps = SequenceParameterSets.AddDefaulted_GetRef();
					sps.Reserve(spsLen);
					sps.SetNumUninitialized(spsLen);
					if (!ByteReader.ReadBytes(sps.GetData(), spsLen))
					{
						return false;
					}
					TotalSPSSize += 4 + spsLen;		// 4 because we always use 32 bit startcode and not NALUnitLength
					ParseH264SPS(ParsedSPSs.AddDefaulted_GetRef(), sps.GetData(), sps.Num());
				}
				uint8 nPPS = 0;
				if (!ByteReader.ReadByte(nPPS))
				{
					return false;
				}
				if (nPPS)
				{
					PictureParameterSets.Reserve(nPPS);
				}
				int32 TotalPPSSize = 0;
				for(int32 i = 0; i < nPPS; ++i)
				{
					uint16 ppsLen = 0;
					if (!ByteReader.ReadByte(ppsLen))
					{
						return false;
					}
					TArray<uint8>& pps = PictureParameterSets.AddDefaulted_GetRef();
					pps.Reserve(ppsLen);
					pps.SetNumUninitialized(ppsLen);
					if (!ByteReader.ReadBytes(pps.GetData(), ppsLen))
					{
						return false;
					}
					TotalPPSSize += 4 + ppsLen;		// 4 because we always use 32 bit startcode and not NALUnitLength
				}

				if (AVCProfileIndication == 100 || AVCProfileIndication == 110 || AVCProfileIndication == 122 || AVCProfileIndication == 144)
				{
					// At least according to the ISO 14496-15:2014 standard these values must appear.
					// I do have however files that are of AVC profile 100 but omit these values.
					// Therefore let's do a quick check if we can read at least 4 more bytes.
					if (ByteReader.BytesRemaining() >= 4)
					{
						if (!ByteReader.ReadByte(ChromaFormat))
						{
							return false;
						}
						ChromaFormat &= 3;
						if (!ByteReader.ReadByte(BitDepthLumaMinus8))
						{
							return false;
						}
						BitDepthLumaMinus8 &= 7;
						if (!ByteReader.ReadByte(BitDepthChromaMinus8))
						{
							return false;
						}
						BitDepthChromaMinus8 &= 7;
						if (!ByteReader.ReadByte(nSPS))
						{
							return false;
						}
						if (nSPS)
						{
							SequenceParameterSetsExt.Reserve(nSPS);
						}
						for(int32 i = 0; i < nSPS; ++i)
						{
							uint16 spsLenExt = 0;
							if (!ByteReader.ReadByte(spsLenExt))
							{
								return false;
							}
							TArray<uint8>& spsExt = SequenceParameterSetsExt.AddDefaulted_GetRef();
							spsExt.Reserve(spsLenExt);
							spsExt.SetNumUninitialized(spsLenExt);
							if (!ByteReader.ReadBytes(spsExt.GetData(), spsLenExt))
							{
								return false;
							}
						}
						bHaveAdditionalProfileIndication = true;
					}
				}

				int32 TotalCSDSize = TotalSPSSize + TotalPPSSize;
				if (TotalCSDSize)
				{
					CodecSpecificData.Reserve(TotalCSDSize);
					CodecSpecificDataSPSOnly.Reserve(TotalSPSSize);
					for(int32 i = 0; i < SequenceParameterSets.Num(); ++i)
					{
						CodecSpecificDataSPSOnly.Push(0);
						CodecSpecificDataSPSOnly.Push(0);
						CodecSpecificDataSPSOnly.Push(0);
						CodecSpecificDataSPSOnly.Push(1);
						CodecSpecificData.Push(0);
						CodecSpecificData.Push(0);
						CodecSpecificData.Push(0);
						CodecSpecificData.Push(1);
						for(int32 j = 0, jMax = SequenceParameterSets[i].Num(); j < jMax; ++j)
						{
							CodecSpecificDataSPSOnly.Push((SequenceParameterSets[i].GetData())[j]);
							CodecSpecificData.Push((SequenceParameterSets[i].GetData())[j]);
						}
					}
					CodecSpecificDataPPSOnly.Reserve(TotalPPSSize);
					for(int32 i = 0; i < PictureParameterSets.Num(); ++i)
					{
						CodecSpecificDataPPSOnly.Push(0);
						CodecSpecificDataPPSOnly.Push(0);
						CodecSpecificDataPPSOnly.Push(0);
						CodecSpecificDataPPSOnly.Push(1);
						CodecSpecificData.Push(0);
						CodecSpecificData.Push(0);
						CodecSpecificData.Push(0);
						CodecSpecificData.Push(1);
						for(int32 j = 0, jMax = PictureParameterSets[i].Num(); j < jMax; ++j)
						{
							CodecSpecificDataPPSOnly.Push((PictureParameterSets[i].GetData())[j]);
							CodecSpecificData.Push((PictureParameterSets[i].GetData())[j]);
						}
					}
				}

				return true;
			}
			return false;
		}



		void FHEVCDecoderConfigurationRecord::SetRawData(const void* Data, int64 Size)
		{
			RawData.Empty();
			if (Size)
			{
				RawData.Reserve((uint32)Size);
				RawData.SetNumUninitialized((uint32)Size);
				FMemory::Memcpy(RawData.GetData(), Data, Size);
			}
		}

		const TArray<uint8>& FHEVCDecoderConfigurationRecord::GetRawData() const
		{
			return RawData;
		}

		const TArray<uint8>& FHEVCDecoderConfigurationRecord::GetCodecSpecificData() const
		{
			return CodecSpecificData;
		}

		int32 FHEVCDecoderConfigurationRecord::GetNumberOfSPS() const
		{
			return ParsedSPSs.Num();
		}

		const FISO23008_2_seq_parameter_set_data& FHEVCDecoderConfigurationRecord::GetParsedSPS(int32 SpsIndex) const
		{
			return ParsedSPSs[SpsIndex];
		}

		void FHEVCDecoderConfigurationRecord::Reset()
		{
			RawData.Empty();
			CodecSpecificData.Empty();
			Arrays.Empty();
			ConfigurationVersion = 0;
			GeneralProfileSpace = 0;
			GeneralTierFlag = 0;
			GeneralProfileIDC = 0;
			GeneralProfileCompatibilityFlags = 0;
			GeneralConstraintIndicatorFlags = 0;
			GeneralLevelIDC = 0;
			MinSpatialSegmentationIDC = 0;
			ParallelismType = 0;
			ChromaFormat = 0;
			BitDepthLumaMinus8 = 0;
			BitDepthChromaMinus8 = 0;
			AverageFrameRate = 0;
			ConstantFrameRate = 0;
			NumTemporalLayers = 0;
			TemporalIdNested = 0;
			NALUnitLengthMinus1 = 0;
			ParsedSPSs.Empty();
		}

		bool FHEVCDecoderConfigurationRecord::CreateFromCodecSpecificData(const TArray<uint8>& InFromCSD)
		{
			if (!InFromCSD.IsEmpty())
			{
				CodecSpecificData = InFromCSD;
				TArray<ElectraDecodersUtil::MPEG::H265::FNaluInfo> Nalus;
				if (!ElectraDecodersUtil::MPEG::H265::ParseBitstreamForNALUs(Nalus, InFromCSD.GetData(), InFromCSD.Num()))
				{
					return false;
				}
				FArray aVPS, aSPS, aPPS;
				for(int32 i=0; i<Nalus.Num(); ++i)
				{
					if (Nalus[i].Type == 32)
					{
						aVPS.NALUnitType = 32;
						aVPS.NALUs.Emplace_GetRef() = MakeArrayView<const uint8>(InFromCSD.GetData() + Nalus[i].Offset + Nalus[i].UnitLength, Nalus[i].Size);
					}
					else if (Nalus[i].Type == 33)
					{
						TMap<uint32, ElectraDecodersUtil::MPEG::H265::FSequenceParameterSet> SPSs;
						if (ElectraDecodersUtil::MPEG::H265::ParseSequenceParameterSet(SPSs, InFromCSD.GetData() + Nalus[i].Offset + Nalus[i].UnitLength, Nalus[i].Size))
						{
							ElectraDecodersUtil::MPEG::H265::FSequenceParameterSet sps(SPSs.CreateConstIterator().Value());
							GeneralProfileSpace = sps.profile_tier_level.general_profile_space;
							GeneralTierFlag = sps.profile_tier_level.general_tier_flag;
							GeneralProfileIDC = sps.profile_tier_level.general_profile_idc;
							GeneralProfileCompatibilityFlags = sps.profile_tier_level.general_profile_compatibility_flags;
							GeneralConstraintIndicatorFlags = sps.profile_tier_level.general_constraint_indicator_flags;
							GeneralLevelIDC = sps.profile_tier_level.general_level_idc;
							check(sps.vui_parameters_present_flag);
							MinSpatialSegmentationIDC = sps.vui_parameters.min_spatial_segmentation_idc;
							ParallelismType = 0;	// for simplicity as 0 indicates `unknown`.
							ChromaFormat = sps.chroma_format_idc;
							BitDepthLumaMinus8 = sps.bit_depth_luma_minus8;
							BitDepthChromaMinus8 = sps.bit_depth_chroma_minus8;
							AverageFrameRate = 0;	// unknown
							ConstantFrameRate = 0;	// may or may not
							NumTemporalLayers = 0;	// unknown
							TemporalIdNested = 0;	// may or may not
							NALUnitLengthMinus1 = 3;
						}
						else
						{
							return false;
						}
						aSPS.NALUnitType = 33;
						aSPS.NALUs.Emplace_GetRef() = MakeArrayView<const uint8>(InFromCSD.GetData() + Nalus[i].Offset + Nalus[i].UnitLength, Nalus[i].Size);
					}
					else if (Nalus[i].Type == 34)
					{
						aPPS.NALUnitType = 34;
						aPPS.NALUs.Emplace_GetRef() = MakeArrayView<const uint8>(InFromCSD.GetData() + Nalus[i].Offset + Nalus[i].UnitLength, Nalus[i].Size);
					}
				}
				if (aVPS.NALUs.IsEmpty() || aSPS.NALUs.IsEmpty() || aPPS.NALUs.IsEmpty())
				{
					return false;
				}
				Arrays.Emplace(MoveTemp(aVPS));
				Arrays.Emplace(MoveTemp(aSPS));
				Arrays.Emplace(MoveTemp(aPPS));

				// Create the raw configuration record now.
				ConfigurationVersion = 1;
				FElectraBitstreamWriter wr;
				wr.PutBits(ConfigurationVersion, 8);
				wr.PutBits(GeneralProfileSpace, 2);
				wr.PutBits(GeneralTierFlag, 1);
				wr.PutBits(GeneralProfileIDC, 5);
				wr.PutBits(GeneralProfileCompatibilityFlags, 32);
				wr.PutBits64(GeneralConstraintIndicatorFlags, 48);
				wr.PutBits(GeneralLevelIDC, 8);
				wr.PutBits(15U, 4);
				wr.PutBits(MinSpatialSegmentationIDC, 12);
				wr.PutBits(63U, 6);
				wr.PutBits(ParallelismType, 2);
				wr.PutBits(63U, 6);
				wr.PutBits(ChromaFormat, 2);
				wr.PutBits(31U, 5);
				wr.PutBits(BitDepthLumaMinus8, 3);
				wr.PutBits(31U, 5);
				wr.PutBits(BitDepthChromaMinus8, 3);
				wr.PutBits(AverageFrameRate, 16);
				wr.PutBits(ConstantFrameRate, 2);
				wr.PutBits(NumTemporalLayers, 3);
				wr.PutBits(TemporalIdNested, 1);
				wr.PutBits(NALUnitLengthMinus1, 2);
				wr.PutBits(static_cast<uint32>(Arrays.Num()), 8);
				for(int32 i=0; i<Arrays.Num(); ++i)
				{
					const FArray& a(Arrays[i]);
					wr.PutBits(a.Completeness, 1);
					wr.PutBits(0U, 1);
					wr.PutBits(a.NALUnitType, 6);
					wr.PutBits(static_cast<uint32>(a.NALUs.Num()), 16);
					for(int32 j=0; j<a.NALUs.Num(); ++j)
					{
						const TArray<uint8>& d(a.NALUs[j]);
						wr.PutBits(static_cast<uint32>(d.Num()), 16);
						for(int32 k=0; k<d.Num(); ++k)
						{
							wr.PutBits(d[k], 8);
						}
					}
				}
				wr.GetArray(RawData);
				return true;
			}
			return false;
		}

		bool FHEVCDecoderConfigurationRecord::Parse()
		{
			CodecSpecificData.Empty();

			if (RawData.Num() >= 23)
			{
				FElectraBitstreamReader BitReader(RawData.GetData(), RawData.Num());

				ConfigurationVersion = (uint8)BitReader.GetBits(8);
				if (ConfigurationVersion != 1)
				{
					return false;
				}
				GeneralProfileSpace = (uint8)BitReader.GetBits(2);
				GeneralTierFlag = (uint8)BitReader.GetBits(1);
				GeneralProfileIDC = (uint8)BitReader.GetBits(5);
				GeneralProfileCompatibilityFlags  = BitReader.GetBits(32);
				for(int32 i=0; i<48; ++i)
				{
					GeneralConstraintIndicatorFlags = (GeneralConstraintIndicatorFlags << 1U) | uint64(BitReader.GetBits(1));
				}
				GeneralLevelIDC = (uint8)BitReader.GetBits(8);
				if (BitReader.GetBits(4) != 15)
				{
					return false;
				}
				MinSpatialSegmentationIDC = (uint16)BitReader.GetBits(12);
				if (BitReader.GetBits(6) != 63)
				{
					return false;
				}
				ParallelismType = (uint8)BitReader.GetBits(2);
				if (BitReader.GetBits(6) != 63)
				{
					return false;
				}
				ChromaFormat = (uint8)BitReader.GetBits(2);
				if (BitReader.GetBits(5) != 31)
				{
					return false;
				}
				BitDepthLumaMinus8 = (uint8)BitReader.GetBits(3);
				if (BitReader.GetBits(5) != 31)
				{
					return false;
				}
				BitDepthChromaMinus8 = (uint8)BitReader.GetBits(3);
				AverageFrameRate = (uint16)BitReader.GetBits(16);
				ConstantFrameRate = (uint8)BitReader.GetBits(2);
				NumTemporalLayers = (uint8)BitReader.GetBits(3);
				TemporalIdNested = (uint8)BitReader.GetBits(1);
				NALUnitLengthMinus1 = (uint8)BitReader.GetBits(2);
				uint32 numArrays = BitReader.GetBits(8);
				Arrays.Reserve(numArrays);
				for(uint32 i=0; i<numArrays; ++i)
				{
					FArray &a = Arrays.AddDefaulted_GetRef();
					a.Completeness = (uint8)BitReader.GetBits(1);
					if (BitReader.GetBits(1) != 0)
					{
						return false;
					}
					a.NALUnitType = (uint8)BitReader.GetBits(6);
					uint32 numNALUs = BitReader.GetBits(16);
					a.NALUs.Reserve(numNALUs);
					for(uint32 j=0; j<numNALUs; ++j)
					{
						uint16 naluLen = (uint16)BitReader.GetBits(16);
						TArray<uint8> &n = a.NALUs.AddDefaulted_GetRef();
						n.Reserve(naluLen);
						for(uint32 k=0; k<naluLen; ++k)
						{
							n.Emplace((uint8)BitReader.GetBits(8));
						}
					}
				}
				if (BitReader.GetRemainingBits() != 0)
				{
					return false;
				}

				// CSD
				CodecSpecificData.Empty();
				for(int32 i=0; i<Arrays.Num(); ++i)
				{
					const FArray& a = Arrays[i];
					// SPS nut?
					if (a.NALUnitType == 33)
					{
						FISO23008_2_seq_parameter_set_data sps;
						if (ParseH265SPS(sps, a.NALUs[0].GetData(), a.NALUs[0].Num()))
						{
							ParsedSPSs.Emplace(MoveTemp(sps));
						}
					}

					for(int32 j=0; j<a.NALUs.Num(); ++j)
					{
						CodecSpecificData.Add(0);
						CodecSpecificData.Add(0);
						CodecSpecificData.Add(0);
						CodecSpecificData.Add(1);
						CodecSpecificData.Append(a.NALUs[j]);
					}
				}
				return true;
			}
			return false;
		}



		static int32 FindStartCode(const uint8* InData, SIZE_T InDataSize, int32& NALUnitLength)
		{
			for(const uint8* Data = InData; InDataSize >= 3; ++Data, --InDataSize)
			{
				if (Data[0] == 0 && Data[1] == 0 && (Data[2] == 1 || (InDataSize >= 4 && Data[2] == 0 && Data[3] == 1)))
				{
					NALUnitLength = Data[2] ? 3 : 4;
					return Data - reinterpret_cast<const uint8*>(InData);
				}
			}
			NALUnitLength = -1;
			return -1;
		}



		void ParseBitstreamForNALUs(TArray<FNaluInfo>& outNALUs, const void* InBitstream, uint64 InBitstreamLength)
		{
			outNALUs.Reset();

			uint64 Pos = 0;
			uint64 BytesToGo = InBitstreamLength;
			const uint8* BitstreamData = (const uint8*)InBitstream;
			while(1)
			{
				int32 UnitLength = 0, StartCodePos = FindStartCode(BitstreamData, BytesToGo, UnitLength);
				if (StartCodePos >= 0)
				{
					if (outNALUs.Num())
					{
						outNALUs.Last().Size = StartCodePos;
						outNALUs.Last().Type = *BitstreamData;
					}
					FNaluInfo n;
					n.Offset = Pos + StartCodePos;
					n.Size = 0;
					n.Type = 0;
					n.UnitLength = UnitLength;
					outNALUs.Push(n);
					BitstreamData = AdvancePointer(BitstreamData, StartCodePos + UnitLength);
					Pos += StartCodePos + UnitLength;
					BytesToGo -= StartCodePos + UnitLength;
				}
				else
				{
					if (outNALUs.Num())
					{
						outNALUs.Last().Size = BytesToGo;
						outNALUs.Last().Type = *BitstreamData;
					}
					break;
				}
			}

		}



		int32 EBSPtoRBSP(uint8* OutBuf, const uint8* InBuf, int32 NumBytesIn)
		{
			uint8* OutBase = OutBuf;
			while(NumBytesIn-- > 0)
			{
				uint8 b = *InBuf++;
				*OutBuf++ = b;
				if (b == 0)
				{
					if (NumBytesIn > 1)
					{
						if (InBuf[0] == 0x00 && InBuf[1] == 0x03)
						{
							*OutBuf++ = 0x00;
							InBuf += 2;
							NumBytesIn -= 2;
						}
					}
				}
			}
			return OutBuf - OutBase;
		}



		template <typename T>
		struct FScopedDataPtr
		{
			FScopedDataPtr(void* Addr)
				: Data(static_cast<T*>(Addr))
			{
			}
			~FScopedDataPtr()
			{
				FMemory::Free(static_cast<void*>(Data));
			}
			operator T* ()
			{
				return Data;
			}
			T* Data;
		};


		bool ParseH264SPS(FISO14496_10_seq_parameter_set_data& OutSPS, const void* Data, int32 Size)
		{
			struct FSyntaxElement
			{
				static uint32 ue_v(FElectraBitstreamReader& BitStream)
				{
					int32 lz = -1;
					for(uint32 b = 0; b == 0; ++lz)
					{
						b = BitStream.GetBits(1);
					}
					if (lz)
					{
						return ((1 << lz) | BitStream.GetBits(lz)) - 1;
					}
					return 0;
				}
				static int32 se_v(FElectraBitstreamReader& BitStream)
				{
					uint32 c = ue_v(BitStream);
					return c & 1 ? int32((c + 1) >> 1) : -int32((c + 1) >> 1);
				}
			};

			// SPS is usually an EBSP so we need to strip it down.
			FScopedDataPtr<uint8> RBSP(FMemory::Malloc(Size));
			int32 RBSPsize = EBSPtoRBSP(RBSP, static_cast<const uint8*>(Data), Size);
			FElectraBitstreamReader BitReader(RBSP, RBSPsize);

			FMemory::Memzero(OutSPS);

			uint8 nalUnitType = (uint8)BitReader.GetBits(8);
			if ((nalUnitType & 0x1f) != 0x7)	// SPS NALU?
			{
				return false;
			}

			OutSPS.profile_idc = (uint8)BitReader.GetBits(8);
			OutSPS.constraint_set0_flag = (uint8)BitReader.GetBits(1);
			OutSPS.constraint_set1_flag = (uint8)BitReader.GetBits(1);
			OutSPS.constraint_set2_flag = (uint8)BitReader.GetBits(1);
			OutSPS.constraint_set3_flag = (uint8)BitReader.GetBits(1);
			OutSPS.constraint_set4_flag = (uint8)BitReader.GetBits(1);
			OutSPS.constraint_set5_flag = (uint8)BitReader.GetBits(1);
			BitReader.SkipBits(2);
			OutSPS.level_idc = (uint8)BitReader.GetBits(8);
			OutSPS.seq_parameter_set_id = FSyntaxElement::ue_v(BitReader);
			if (OutSPS.profile_idc == 100 || OutSPS.profile_idc == 110 || OutSPS.profile_idc == 122 || OutSPS.profile_idc == 244 ||
				OutSPS.profile_idc == 44 || OutSPS.profile_idc == 83 || OutSPS.profile_idc == 86 || OutSPS.profile_idc == 118 || OutSPS.profile_idc == 128 ||
				OutSPS.profile_idc == 138 || OutSPS.profile_idc == 139 || OutSPS.profile_idc == 134 || OutSPS.profile_idc == 135)
			{
				OutSPS.chroma_format_idc = FSyntaxElement::ue_v(BitReader);
				if (OutSPS.chroma_format_idc == 3)
				{
					OutSPS.separate_colour_plane_flag = (uint8)BitReader.GetBits(1);
				}
				OutSPS.bit_depth_luma_minus8 = FSyntaxElement::ue_v(BitReader);
				OutSPS.bit_depth_chroma_minus8 = FSyntaxElement::ue_v(BitReader);
				OutSPS.qpprime_y_zero_transform_bypass_flag = (uint8)BitReader.GetBits(1);
				OutSPS.seq_scaling_matrix_present_flag = (uint8)BitReader.GetBits(1);
				if (OutSPS.seq_scaling_matrix_present_flag)
				{
					auto scaling_list = [&BitReader](int32* scalingList, int32 sizeOfScalingList, bool& useDefaultScalingMatrixFlag) -> void
					{
						int32 lastScale = 8;
						int32 nextScale = 8;
						for(int32 j=0; j<sizeOfScalingList; ++j)
						{
							if (nextScale)
							{
								int32 delta_scale = FSyntaxElement::se_v(BitReader);
								nextScale = (lastScale + delta_scale + 256) % 256;
								useDefaultScalingMatrixFlag = (j == 0 && nextScale == 0);
							}
							scalingList[j] = (nextScale == 0) ? lastScale : nextScale;
							lastScale = scalingList[j];
						}
					};

					// Skip over the scaling matrices.
					int32 dummyScalingMatrix[64] = {0};
					bool bDummyDefaultScalingMatrixFlag = false;
					for(int32 i=0, iMax=OutSPS.chroma_format_idc!=3?8:12; i<iMax; ++i)
					{
						uint8 seq_scaling_list_present_flag = (uint8)BitReader.GetBits(1);
						if (seq_scaling_list_present_flag)
						{
							if (i < 6)
							{
								scaling_list(dummyScalingMatrix, 16, bDummyDefaultScalingMatrixFlag);
							}
							else
							{
								scaling_list(dummyScalingMatrix, 64, bDummyDefaultScalingMatrixFlag);
							}
						}
					}
				}
			}
			OutSPS.log2_max_frame_num_minus4 = FSyntaxElement::ue_v(BitReader);
			OutSPS.pic_order_cnt_type = FSyntaxElement::ue_v(BitReader);
			if (OutSPS.pic_order_cnt_type == 0)
			{
				OutSPS.log2_max_pic_order_cnt_lsb_minus4 = FSyntaxElement::ue_v(BitReader);
			}
			else if (OutSPS.pic_order_cnt_type == 1)
			{
				OutSPS.delta_pic_order_always_zero_flag = FSyntaxElement::ue_v(BitReader);
				OutSPS.offset_for_non_ref_pic = FSyntaxElement::se_v(BitReader);
				OutSPS.offset_for_top_to_bottom_field = FSyntaxElement::se_v(BitReader);
				OutSPS.num_ref_frames_in_pic_order_cnt_cycle = FSyntaxElement::ue_v(BitReader);
				for(uint32 i = 0; i < OutSPS.num_ref_frames_in_pic_order_cnt_cycle; ++i)
				{
					FSyntaxElement::se_v(BitReader);		// discard
				}
			}
			OutSPS.max_num_ref_frames = FSyntaxElement::ue_v(BitReader);
			OutSPS.gaps_in_frame_num_value_allowed_flag = (uint8)BitReader.GetBits(1);
			OutSPS.pic_width_in_mbs_minus1 = FSyntaxElement::ue_v(BitReader);
			OutSPS.pic_height_in_map_units_minus1 = FSyntaxElement::ue_v(BitReader);
			OutSPS.frame_mbs_only_flag = (uint8)BitReader.GetBits(1);
			if (!OutSPS.frame_mbs_only_flag)
			{
				OutSPS.mb_adaptive_frame_field_flag = (uint8)BitReader.GetBits(1);
			}
			OutSPS.direct_8x8_inference_flag = (uint8)BitReader.GetBits(1);
			OutSPS.frame_cropping_flag = (uint8)BitReader.GetBits(1);
			if (OutSPS.frame_cropping_flag)
			{
				OutSPS.frame_crop_left_offset = FSyntaxElement::ue_v(BitReader);
				OutSPS.frame_crop_right_offset = FSyntaxElement::ue_v(BitReader);
				OutSPS.frame_crop_top_offset = FSyntaxElement::ue_v(BitReader);
				OutSPS.frame_crop_bottom_offset = FSyntaxElement::ue_v(BitReader);
			}
			OutSPS.vui_parameters_present_flag = (uint8)BitReader.GetBits(1);
			if (OutSPS.vui_parameters_present_flag)
			{
				OutSPS.aspect_ratio_info_present_flag = (uint8)BitReader.GetBits(1);
				if (OutSPS.aspect_ratio_info_present_flag)
				{
					OutSPS.aspect_ratio_idc = (uint8)BitReader.GetBits(8);
					if (OutSPS.aspect_ratio_idc == 255)
					{
						OutSPS.sar_width = (uint16)BitReader.GetBits(16);
						OutSPS.sar_height = (uint16)BitReader.GetBits(16);
					}
				}
				OutSPS.overscan_info_present_flag = (uint8)BitReader.GetBits(1);
				if (OutSPS.overscan_info_present_flag)
				{
					OutSPS.overscan_appropriate_flag = (uint8)BitReader.GetBits(1);
				}
				OutSPS.video_signal_type_present_flag = (uint8)BitReader.GetBits(1);
				if (OutSPS.video_signal_type_present_flag)
				{
					OutSPS.video_format = (uint8)BitReader.GetBits(3);
					OutSPS.video_full_range_flag = (uint8)BitReader.GetBits(1);
					OutSPS.colour_description_present_flag = (uint8)BitReader.GetBits(1);
					if (OutSPS.colour_description_present_flag)
					{
						OutSPS.colour_primaries = (uint8)BitReader.GetBits(8);
						OutSPS.transfer_characteristics = (uint8)BitReader.GetBits(8);
						OutSPS.matrix_coefficients = (uint8)BitReader.GetBits(8);
					}
				}
				OutSPS.chroma_loc_info_present_flag = (uint8)BitReader.GetBits(1);
				if (OutSPS.chroma_loc_info_present_flag)
				{
					OutSPS.chroma_sample_loc_type_top_field = FSyntaxElement::ue_v(BitReader);
					OutSPS.chroma_sample_loc_type_bottom_field = FSyntaxElement::ue_v(BitReader);
				}
				OutSPS.timing_info_present_flag = (uint8)BitReader.GetBits(1);
				if (OutSPS.timing_info_present_flag)
				{
					OutSPS.num_units_in_tick = BitReader.GetBits(32);
					OutSPS.time_scale = BitReader.GetBits(32);
					OutSPS.fixed_frame_rate_flag = (uint8)BitReader.GetBits(1);
				}
				// The remainder is of no interest to us at the moment.
			}
			return true;
		}


		//! Parses a H.265 (ISO/IEC 23008-2) SPS NALU.
		bool ParseH265SPS(FISO23008_2_seq_parameter_set_data& OutSPS, const void* Data, int32 Size)
		{
			struct FSyntaxElement
			{
				static uint32 ue_v(FElectraBitstreamReader& BitStream)
				{
					int32 lz = -1;
					for(uint32 b = 0; b == 0; ++lz)
					{
						b = BitStream.GetBits(1);
					}
					if (lz)
					{
						return ((1 << lz) | BitStream.GetBits(lz)) - 1;
					}
					return 0;
				}
				static int32 se_v(FElectraBitstreamReader& BitStream)
				{
					uint32 c = ue_v(BitStream);
					return c & 1 ? int32((c + 1) >> 1) : -int32((c + 1) >> 1);
				}
			};

			// SPS is usually an EBSP so we need to strip it down.
			FScopedDataPtr<uint8> RBSP(FMemory::Malloc(Size));
			int32 RBSPsize = EBSPtoRBSP(RBSP, static_cast<const uint8*>(Data), Size);
			FElectraBitstreamReader BitReader(RBSP, RBSPsize);

			FMemory::Memzero(OutSPS);

			if (BitReader.GetBits(1) != 0)	// forbidden_zero_bit
			{
				return false;
			}
			if (BitReader.GetBits(6) != 33)	// sps_nut ?
			{
				return false;
			}
			BitReader.SkipBits(6);			// nuh_layer_id
			BitReader.SkipBits(3);			// nuh_temporal_id_plus1

			OutSPS.sps_video_parameter_set_id = BitReader.GetBits(4);
			OutSPS.sps_max_sub_layers_minus1 = BitReader.GetBits(3);
			OutSPS.sps_temporal_id_nesting_flag = BitReader.GetBits(1);

			// profile_tier_level( sps_max_sub_layers_minus1 )
			OutSPS.general_profile_space = BitReader.GetBits(2);
			OutSPS.general_tier_flag = BitReader.GetBits(1);
			OutSPS.general_profile_idc = BitReader.GetBits(5);
			for(int32 i=0; i<32; ++i)
			{
				OutSPS.general_profile_compatibility_flag = (OutSPS.general_profile_compatibility_flag << 1) | BitReader.GetBits(1);
			}
			OutSPS.general_progressive_source_flag = BitReader.GetBits(1);
			OutSPS.general_interlaced_source_flag = BitReader.GetBits(1);
			OutSPS.general_non_packed_constraint_flag = BitReader.GetBits(1);
			OutSPS.general_frame_only_constraint_flag = BitReader.GetBits(1);
			// We skip over the next 44 bits that vary in layout depending on general_profile_idc and general_profile_compatibility_flag.
			// For the moment none of the flags interest us, so we do not parse them for simplicity.
			OutSPS.general_reserved_zero_44bits = ((uint64)BitReader.GetBits(32) << 32) | BitReader.GetBits(12);

			OutSPS.general_level_idc = BitReader.GetBits(8);
			for(uint32 i=0; i<OutSPS.sps_max_sub_layers_minus1; ++i)
			{
				OutSPS.sub_layer_profile_present_flag[i] = BitReader.GetBits(1);
				OutSPS.sub_layer_level_present_flag[i] = BitReader.GetBits(1);
			}
			if (OutSPS.sps_max_sub_layers_minus1 > 0)
			{
				for(uint32 i = OutSPS.sps_max_sub_layers_minus1; i<8; ++i)
				{
					BitReader.SkipBits(2);		// reserved_zero_2bits[ i ]
				}
			}
			for(uint32 i=0; i<OutSPS.sps_max_sub_layers_minus1; ++i)
			{
				// Similarly, we skip all of these flags that are of no interest to us right now.
				if (OutSPS.sub_layer_profile_present_flag[i])
				{
					BitReader.SkipBits(2);		// sub_layer_profile_space[ i ]
					BitReader.SkipBits(1);		// sub_layer_tier_flag[ i ]
					BitReader.SkipBits(5);		// sub_layer_profile_idc[ i ]
					BitReader.SkipBits(32);		// for( j = 0; j < 32; j++ ) sub_layer_profile_compatibility_flag[ i ][ j ] u(1)
					BitReader.SkipBits(1);		// sub_layer_progressive_source_flag[ i ]
					BitReader.SkipBits(1);		// sub_layer_interlaced_source_flag[ i ]
					BitReader.SkipBits(1);		// sub_layer_non_packed_constraint_flag[ i ]
					BitReader.SkipBits(1);		// sub_layer_frame_only_constraint_flag[ i ]
					BitReader.SkipBits(44);		// sub_layer_reserved_zero_44bits[ i ]
				}
				if (OutSPS.sub_layer_level_present_flag[i])
				{
					BitReader.SkipBits(8);		// sub_layer_level_idc[ i ]
				}
			}

			OutSPS.sps_seq_parameter_set_id = FSyntaxElement::ue_v(BitReader);
			OutSPS.chroma_format_idc = FSyntaxElement::ue_v(BitReader);
			if (OutSPS.chroma_format_idc == 3)
			{
				OutSPS.separate_colour_plane_flag = BitReader.GetBits(1);			// separate_colour_plane_flag
			}
			OutSPS.pic_width_in_luma_samples = FSyntaxElement::ue_v(BitReader);
			OutSPS.pic_height_in_luma_samples = FSyntaxElement::ue_v(BitReader);

			OutSPS.conformance_window_flag = BitReader.GetBits(1);
			if (OutSPS.conformance_window_flag)
			{
				OutSPS.conf_win_left_offset = FSyntaxElement::ue_v(BitReader);
				OutSPS.conf_win_right_offset = FSyntaxElement::ue_v(BitReader);
				OutSPS.conf_win_top_offset = FSyntaxElement::ue_v(BitReader);
				OutSPS.conf_win_bottom_offset = FSyntaxElement::ue_v(BitReader);
			}
			OutSPS.bit_depth_luma_minus8 = FSyntaxElement::ue_v(BitReader);
			OutSPS.bit_depth_chroma_minus8 = FSyntaxElement::ue_v(BitReader);
			OutSPS.log2_max_pic_order_cnt_lsb_minus4 = FSyntaxElement::ue_v(BitReader);
			OutSPS.sps_sub_layer_ordering_info_present_flag = BitReader.GetBits(1);
			for(uint32 i=(OutSPS.sps_sub_layer_ordering_info_present_flag ? 0 : OutSPS.sps_max_sub_layers_minus1); i<=OutSPS.sps_max_sub_layers_minus1; ++i)
			{
				OutSPS.sps_max_dec_pic_buffering_minus1[i] = FSyntaxElement::ue_v(BitReader);
				OutSPS.sps_max_num_reorder_pics[i] = FSyntaxElement::ue_v(BitReader);
				OutSPS.sps_max_latency_increase_plus1[i] = FSyntaxElement::ue_v(BitReader);
			}
			OutSPS.log2_min_luma_coding_block_size_minus3 = FSyntaxElement::ue_v(BitReader);
			OutSPS.log2_diff_max_min_luma_coding_block_size = FSyntaxElement::ue_v(BitReader);
			OutSPS.log2_min_transform_block_size_minus2 = FSyntaxElement::ue_v(BitReader);
			OutSPS.log2_diff_max_min_transform_block_size = FSyntaxElement::ue_v(BitReader);
			OutSPS.max_transform_hierarchy_depth_inter = FSyntaxElement::ue_v(BitReader);
			OutSPS.max_transform_hierarchy_depth_intra = FSyntaxElement::ue_v(BitReader);
			OutSPS.scaling_list_enabled_flag = BitReader.GetBits(1);
			if (OutSPS.scaling_list_enabled_flag)
			{
				// Parse the scaling list into temp variables only. We do not need them right now.
				OutSPS.sps_scaling_list_data_present_flag = BitReader.GetBits(1);
				if (OutSPS.sps_scaling_list_data_present_flag)
				{
					// scaling_list_data( )
					for(uint32 sizeId=0; sizeId<4; ++sizeId)
					{
						for(int32 matrixId=0; matrixId<((sizeId==3)?2:6); ++matrixId)
						{
							uint8 scaling_list_pred_mode_flag = BitReader.GetBits(1);	// [ sizeId ][ matrixId ] u(1)
							if (!scaling_list_pred_mode_flag/*[sizeId][matrixId]*/)
							{
								uint32 scaling_list_pred_matrix_id_delta/*[sizeId][matrixId]*/ = FSyntaxElement::ue_v(BitReader);
							}
							else
							{
								//nextCoef = 8;
								uint32 coefNum = Min((uint32)64U, (uint32)(1 << (4 + (sizeId << 1))));
								if (sizeId > 1)
								{
									uint32 scaling_list_dc_coef_minus8/*[sizeId ? 2][matrixId]*/ = FSyntaxElement::se_v(BitReader);
									//nextCoef = scaling_list_dc_coef_minus8[sizeId ? 2][matrixId] + 8;
								}
								for(uint32 i=0; i<coefNum; i++)
								{
									uint32 scaling_list_delta_coef = FSyntaxElement::se_v(BitReader);
									//nextCoef = ( nextCoef + scaling_list_delta_coef + 256 ) % 256
									//ScalingList[ sizeId ][ matrixId ][ i ] = nextCoef
								}
							}
						}
					}
				}
			}

			OutSPS.amp_enabled_flag = BitReader.GetBits(1);
			OutSPS.sample_adaptive_offset_enabled_flag = BitReader.GetBits(1);
			OutSPS.pcm_enabled_flag = BitReader.GetBits(1);
			if (OutSPS.pcm_enabled_flag)
			{
				OutSPS.pcm_sample_bit_depth_luma_minus1 = BitReader.GetBits(4);
				OutSPS.pcm_sample_bit_depth_chroma_minus1 = BitReader.GetBits(4);
				OutSPS.log2_min_pcm_luma_coding_block_size_minus3 = FSyntaxElement::ue_v(BitReader);
				OutSPS.log2_diff_max_min_pcm_luma_coding_block_size = FSyntaxElement::ue_v(BitReader);
				OutSPS.pcm_loop_filter_disabled_flag = BitReader.GetBits(1);
			}

			OutSPS.num_short_term_ref_pic_sets = FSyntaxElement::ue_v(BitReader);
			uint32 unused_num_delta_pocs[64] = {0};
			for(uint32 i=0; i<OutSPS.num_short_term_ref_pic_sets; ++i)
			{
				struct short_term_ref_pic_set
				{
					static bool parse(uint32 stRpsIdx, uint32 num_short_term_ref_pic_sets, uint32* num_delta_pocs, FElectraBitstreamReader& BitReader)
					{
						uint32 inter_ref_pic_set_prediction_flag = stRpsIdx != 0 ? BitReader.GetBits(1) : 0;
						if (inter_ref_pic_set_prediction_flag)
						{
							uint32 delta_idx_minus1 = 0;
							if (stRpsIdx == num_short_term_ref_pic_sets)
							{
								delta_idx_minus1 = FSyntaxElement::ue_v(BitReader);
							}
							BitReader.SkipBits(1);	// delta_rps_sign
							uint32 abs_delta_rps_minus1 = FSyntaxElement::ue_v(BitReader);
							uint32 RefRpsIdx = stRpsIdx - (delta_idx_minus1 + 1);
							num_delta_pocs[stRpsIdx] = 0;
							for(uint32 j=0; j<=num_delta_pocs[RefRpsIdx]; ++j)
							{
								uint8 used_by_curr_pic_flag/*[j]*/ = BitReader.GetBits(1);
								uint8 use_delta_flag = 0;
								if (!used_by_curr_pic_flag/*[j]*/)
								{
									use_delta_flag = BitReader.GetBits(1);
								}
								if (used_by_curr_pic_flag || use_delta_flag)
								{
									++num_delta_pocs[stRpsIdx];
								}
							}
						}
						else
						{
							uint32 num_negative_pics = FSyntaxElement::ue_v(BitReader);
							uint32 num_positive_pics = FSyntaxElement::ue_v(BitReader);

							if (((uint64)num_positive_pics + (uint64)num_negative_pics) * 2 > BitReader.GetRemainingBits())
							{
								return false;
							}

							num_delta_pocs[stRpsIdx] = num_negative_pics + num_positive_pics;

							for(uint32 j=0; j<num_negative_pics; ++j)
							{
								uint32 delta_poc_s0_minus1/*[j]*/ = FSyntaxElement::ue_v(BitReader);
								(void)delta_poc_s0_minus1;
								BitReader.SkipBits(1);				// used_by_curr_pic_s0_flag[ j ]
							}
							for(uint32 j=0; j<num_positive_pics; ++j)
							{
								uint32 delta_poc_s1_minus1/*[j]*/ = FSyntaxElement::ue_v(BitReader);
								(void)delta_poc_s1_minus1;
								BitReader.SkipBits(1);				// used_by_curr_pic_s1_flag[ j ]
							}
						}
						return true;
					}
				};
				if (!short_term_ref_pic_set::parse(i, OutSPS.num_short_term_ref_pic_sets, unused_num_delta_pocs, BitReader))
				{
					return false;
				}
			}

			uint8 long_term_ref_pics_present_flag = BitReader.GetBits(1);
			if (long_term_ref_pics_present_flag)
			{
				uint32 num_long_term_ref_pics_sps = FSyntaxElement::ue_v(BitReader);
				for(uint32 i=0; i<num_long_term_ref_pics_sps; ++i)
				{
					uint32 lt_ref_pic_poc_lsb_sps = FSyntaxElement::ue_v(BitReader);	//[ i ] u(v)
					BitReader.SkipBits(1);							//	used_by_curr_pic_lt_sps_flag[ i ]
				}
			}

			OutSPS.sps_temporal_mvp_enabled_flag = BitReader.GetBits(1);
			OutSPS.strong_intra_smoothing_enabled_flag = BitReader.GetBits(1);
			OutSPS.vui_parameters_present_flag = BitReader.GetBits(1);
			if (OutSPS.vui_parameters_present_flag)
			{
				// vui_parameters()
				OutSPS.aspect_ratio_info_present_flag = BitReader.GetBits(1);
				if (OutSPS.aspect_ratio_info_present_flag)
				{
					OutSPS.aspect_ratio_idc = BitReader.GetBits(8);
					if (OutSPS.aspect_ratio_idc == 255)
					{
						OutSPS.sar_width = BitReader.GetBits(16);
						OutSPS.sar_height = BitReader.GetBits(16);
					}
				}
				OutSPS.overscan_info_present_flag = BitReader.GetBits(1);
				if (OutSPS.overscan_info_present_flag)
				{
					OutSPS.overscan_appropriate_flag = BitReader.GetBits(1);
				}
				OutSPS.video_signal_type_present_flag = BitReader.GetBits(1);
				if (OutSPS.video_signal_type_present_flag)
				{
					OutSPS.video_format = BitReader.GetBits(3);
					OutSPS.video_full_range_flag = BitReader.GetBits(1);
					OutSPS.colour_description_present_flag = BitReader.GetBits(1);
					if (OutSPS.colour_description_present_flag)
					{
						OutSPS.colour_primaries = BitReader.GetBits(8);
						OutSPS.transfer_characteristics = BitReader.GetBits(8);
						OutSPS.matrix_coeffs = BitReader.GetBits(8);
					}
				}
				OutSPS.chroma_loc_info_present_flag = BitReader.GetBits(1);
				if (OutSPS.chroma_loc_info_present_flag)
				{
					OutSPS.chroma_sample_loc_type_top_field = FSyntaxElement::ue_v(BitReader);
					OutSPS.chroma_sample_loc_type_bottom_field = FSyntaxElement::ue_v(BitReader);
				}
				OutSPS.neutral_chroma_indication_flag = BitReader.GetBits(1);
				OutSPS.field_seq_flag = BitReader.GetBits(1);
				OutSPS.frame_field_info_present_flag = BitReader.GetBits(1);
				OutSPS.default_display_window_flag = BitReader.GetBits(1);
				if (OutSPS.default_display_window_flag)
				{
					OutSPS.def_disp_win_left_offset = FSyntaxElement::ue_v(BitReader);
					OutSPS.def_disp_win_right_offset = FSyntaxElement::ue_v(BitReader);
					OutSPS.def_disp_win_top_offset = FSyntaxElement::ue_v(BitReader);
					OutSPS.def_disp_win_bottom_offset = FSyntaxElement::ue_v(BitReader);
				}
				OutSPS.vui_timing_info_present_flag = BitReader.GetBits(1);
				if (OutSPS.vui_timing_info_present_flag)
				{
					OutSPS.vui_num_units_in_tick = BitReader.GetBits(32);
					OutSPS.vui_time_scale = BitReader.GetBits(32);
					OutSPS.vui_poc_proportional_to_timing_flag = BitReader.GetBits(1);
					if (OutSPS.vui_poc_proportional_to_timing_flag)
					{
						OutSPS.vui_num_ticks_poc_diff_one_minus1 = FSyntaxElement::ue_v(BitReader);
// None of the following is of interest to us now, so we stop parsing at this point.
					}
				}
			}
			return true;
		}


		uint64 FISO23008_2_seq_parameter_set_data::GetConstraintFlags() const
		{
			uint64 ConstraintFlags = ((uint64)((general_progressive_source_flag << 3) | (general_interlaced_source_flag << 2) | (general_non_packed_constraint_flag << 1) | general_frame_only_constraint_flag) << 44) | general_reserved_zero_44bits;
			return ConstraintFlags;
		}

		FString FISO23008_2_seq_parameter_set_data::GetRFC6381(const TCHAR* SampleTypePrefix) const
		{
			// As per ISO/IEC 14496-15:2014 Annex E.3
			uint32 cf = BitReverse32(general_profile_compatibility_flag);
			uint64 ConstraintFlags = GetConstraintFlags();
			FString cfs;
			bool bNonZero = false;
			for(int32 i=5; i>=0; --i, ConstraintFlags >>= 8)
			{
				if ((ConstraintFlags & 255) == 0 && !bNonZero)
				{
					continue;
				}
				bNonZero = true;
				cfs = FString::Printf(TEXT(".%02X"), (uint8)(ConstraintFlags & 255)) + cfs;
			}

			if (general_profile_space == 0)
			{
				return FString::Printf(TEXT("%s.%d.%X.%c%d%s"), SampleTypePrefix, general_profile_idc, cf, general_tier_flag ? TCHAR('H') : TCHAR('L'), general_level_idc, *cfs);
			}
			else
			{
				return FString::Printf(TEXT("%s.%c%d.%X.%c%d%s"), SampleTypePrefix, TCHAR('A')+general_profile_space-1, general_profile_idc, cf, general_tier_flag ? TCHAR('H') : TCHAR('L'), general_level_idc, *cfs);
			}
		}


		bool ExtractSEIMessages(TArray<FSEIMessage>& OutMessages, const void* InBitstream, uint64 InBitstreamLength, ESEIStreamType StreamType, bool bIsPrefixSEI)
		{
			FScopedDataPtr<uint8> RBSP(FMemory::Malloc((uint32) InBitstreamLength));
			int32 RBSPsize = EBSPtoRBSP(RBSP, static_cast<const uint8*>(InBitstream), (int32)InBitstreamLength);
			FElectraBitstreamReader BitReader(RBSP, RBSPsize);

			while(BitReader.GetRemainingByteLength() > 0)
			{
				uint32 next8;
				uint32 payloadType = 0;
				do
				{
					if (BitReader.GetRemainingByteLength() == 0)
					{
						return false;
					}
					next8 = BitReader.GetBits(8);
					payloadType += next8;
				}
				while(next8 == 255);

				uint32 payloadSize = 0;
				do
				{
					if (BitReader.GetRemainingByteLength() == 0)
					{
						return false;
					}
					next8 = BitReader.GetBits(8);
					payloadSize += next8;
				}
				while(next8 == 255);

				check(BitReader.GetRemainingByteLength() >= payloadSize);
				if (BitReader.GetRemainingByteLength() < payloadSize)
				{
					return false;
				}
				FSEIMessage &m = OutMessages.Emplace_GetRef();
				m.PayloadType = payloadType;

				m.Message.Reserve(payloadSize);
				m.Message.SetNumUninitialized(payloadSize);
				if (!BitReader.GetAlignedBytes(m.Message.GetData(), payloadSize))
				{
					return false;
				}

				/*
					We do not parse the SEI messages here, we merely copy their entire payload.
					Therefore, the current position in the bit reader will always be byte-aligned
					with no yet-unhandled bytes remaining in the payload.
					The following checks that are normally in place can therefore be ignored:

						H.265:
							if (more_data_in_payload())		// more_data_in_payload() return false if byte aligned AND all bits consumed, otherwise return true
							{
								if (payload_extension_present())
								{
									reserved_payload_extension_data	// u_v()
								}
								payload_bit_equal_to_one()
								while(!byte_aligned())
								{
									payload_bit_equal_to_zero()
								}
							}

						H.264:
							if (!byte_aligned())
							{
								bit_equal_to_one()
								while(!byte_aligned())
								{
									bit_equal_to_zero()
								}
							}
				*/

				// Check for more_rbsp_data()
				if (BitReader.GetRemainingByteLength() == 1 && BitReader.PeekBits(8) == 0x80)
				{
					break;
				}
			}
			return true;
		}


		bool FSEImastering_display_colour_volume::ParseFromMessage(const FSEIMessage& InMessage)
		{
			if (InMessage.PayloadType == FSEIMessage::EPayloadType::PT_mastering_display_colour_volume && InMessage.Message.Num() >= 24)
			{
				FElectraBitstreamReader BitReader(InMessage.Message.GetData(), InMessage.Message.Num());
				for(int32 i=0; i<3; ++i)
				{
					display_primaries_x[i] = BitReader.GetBits(16);
					display_primaries_y[i] = BitReader.GetBits(16);
				}
				white_point_x = BitReader.GetBits(16);
				white_point_y = BitReader.GetBits(16);
				max_display_mastering_luminance = BitReader.GetBits(32);
				min_display_mastering_luminance = BitReader.GetBits(32);
				return true;
			}
			return false;
		}

		bool FSEIcontent_light_level_info::ParseFromMessage(const FSEIMessage& InMessage)
		{
			if (InMessage.PayloadType == FSEIMessage::EPayloadType::PT_content_light_level_info && InMessage.Message.Num() >= 4)
			{
				FElectraBitstreamReader BitReader(InMessage.Message.GetData(), InMessage.Message.Num());
				max_content_light_level = BitReader.GetBits(16);
				max_pic_average_light_level = BitReader.GetBits(16);
				return true;
			}
			return false;
		}

		bool FSEIalternative_transfer_characteristics::ParseFromMessage(const FSEIMessage& InMessage)
		{
			if (InMessage.PayloadType == FSEIMessage::EPayloadType::PT_alternative_transfer_characteristics && InMessage.Message.Num() >= 1)
			{
				FElectraBitstreamReader BitReader(InMessage.Message.GetData(), InMessage.Message.Num());
				preferred_transfer_characteristics = BitReader.GetBits(8);
				return true;
			}
			return false;
		}

	} // namespace MPEG
} // namespace ElectraDecodersUtil
