// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "HAL/Platform.h"

#include "CoreTypes.h"
#include "CoreGlobals.h"
#include "Templates/SharedPointer.h"
#include "IElectraTextureSample.h"
#include "Math/IntPoint.h"
#include "Math/Range.h"
#include "Misc/Timespan.h"
#include "MediaObjectPool.h"
#include "MediaSamples.h"
#include "MediaVideoDecoderOutputAndroid.h"

class FElectraTextureSampleSupport;
class FEvent;
struct AHardwareBuffer;

/**
 * Texture sample generated by AndroidMedia player.
 */
class ELECTRASAMPLES_API FElectraTextureSample final
	: public IElectraTextureSampleBase
	, public IMediaTextureSampleConverter
	, public TSharedFromThis<FElectraTextureSample, ESPMode::ThreadSafe>
{
public:

	/** Default constructor. */
	FElectraTextureSample(TSharedPtr<FElectraTextureSampleSupport, ESPMode::ThreadSafe> InSupport)
	: Support(InSupport)
	, ImageResources(nullptr)
	, Texture(nullptr)
	, Buffer(nullptr)
	, BufferSize(0)
	{
	}

	/** Virtual destructor. */
	virtual ~FElectraTextureSample();

#if !UE_SERVER
	virtual void ShutdownPoolable() override;
#endif

	void Initialize(FVideoDecoderOutput* InVideoDecoderOutput);

	//~ IMediaTextureSample interface

	virtual const void* GetBuffer() override
	{
		return Buffer;
	}

	virtual EMediaTextureSampleFormat GetFormat() const override;

	virtual uint32 GetStride() const override;

	virtual FRHITexture* GetTexture() const override
	{
		return Texture.GetReference();
	}

	virtual IMediaTextureSampleConverter* GetMediaTextureSampleConverter() override
	{
		return this;
	}

private:
	friend class FElectraTextureSampleSupport;

	FTextureRHIRef InitializeTextureOES(AHardwareBuffer* HardwareBuffer);
	FTextureRHIRef InitializeTextureVulkan(AHardwareBuffer* HardwareBuffer);

	void InitializeTexture(EPixelFormat PixelFormat);
	void SetupFromBuffer(const void* InBuffer, int32 InBufferSize);
	void SetImageResources(jobject InImageResources);
	void CleanupImageResources();
	void CopyFromExternalTextureOES(FRHICommandListImmediate& RHICmdList, FTextureRHIRef& InDstTexture, FTextureRHIRef& InSrcTexture, const FVector2f& Scale, const FVector2f& Offset);
	void CopyFromExternalTextureVulkan(FRHICommandListImmediate& RHICmdList, FTextureRHIRef& InDstTexture, FTextureRHIRef& InSrcTexture, const FVector2f& InScale, const FVector2f& InOffset);

	virtual bool Convert(FRHICommandListImmediate& RHICmdList, FTextureRHIRef& InDstTexture, const FConversionHints& Hints) override;
	bool ConvertCpuOutputPath(FRHICommandListImmediate& RHICmdList, FTextureRHIRef& InDstTexture, const FConversionHints& Hints);
	bool ConvertGpuOutputPath(FRHICommandListImmediate& RHICmdList, FTextureRHIRef& InDstTexture, const FConversionHints& Hints);

	virtual uint32 GetConverterInfoFlags() const
	{
		return ConverterInfoFlags_Default;
	}

	TSharedPtr<FElectraTextureSampleSupport, ESPMode::ThreadSafe> Support;

	FVideoDecoderOutputAndroid* VideoDecoderOutputAndroid;

	bool UseGpuOutputPath;

	void* Buffer;
	int32 BufferSize;

	/** Texture resource. */
	TRefCountPtr<FRHITexture> Texture;

	jobject ImageResources;
};


using FElectraTextureSamplePtr  = TSharedPtr<FElectraTextureSample, ESPMode::ThreadSafe>;
using FElectraTextureSampleRef  = TSharedRef<FElectraTextureSample, ESPMode::ThreadSafe>;

class ELECTRASAMPLES_API FElectraTextureSamplePool : public TMediaObjectPool<FElectraTextureSample, FElectraTextureSamplePool>
{
	using ParentClass = TMediaObjectPool<FElectraTextureSample, FElectraTextureSamplePool>;
	using TextureSample = FElectraTextureSample;

public:
	FElectraTextureSamplePool();

	TextureSample* Alloc() const
	{
		return new TextureSample(Support);
	}

	void PrepareForDecoderShutdown()
	{
	}

	void* GetCodecSurface();

private:
	TSharedPtr<FElectraTextureSampleSupport, ESPMode::ThreadSafe> Support;
};


