// Copyright Epic Games, Inc. All Rights Reserved.

#include "ChaosVDTabsIDs.h"
#include "UObject/NameTypes.h"

const FName FChaosVDTabID::ChaosVisualDebuggerTab = TEXT("ChaosVisualDebuggerTab");
const FName FChaosVDTabID::PlaybackViewport = TEXT("PlaybackViewport");
const FName FChaosVDTabID::WorldOutliner = TEXT("WorldOutliner");
const FName FChaosVDTabID::DetailsPanel = TEXT("DetailsPanel");
const FName FChaosVDTabID::OutputLog = TEXT("ChaosVDOutputLog");
const FName FChaosVDTabID::SolversTrack = TEXT("SolversTrack");
const FName FChaosVDTabID::StatusBar = TEXT("StatusBar");
const FName FChaosVDTabID::CollisionDataDetails = TEXT("CollisionDataDetails");
const FName FChaosVDTabID::SceneQueryDataDetails = TEXT("SceneQueryDataDetails");
const FName FChaosVDTabID::ConstraintsInspector = TEXT("ConstraintsDetails");
const FName FChaosVDTabID::SceneQueryBrowser = TEXT("SceneQueryBrowser");
const FName FChaosVDTabID::RecordedOutputLog = TEXT("RecordedOutputLog");