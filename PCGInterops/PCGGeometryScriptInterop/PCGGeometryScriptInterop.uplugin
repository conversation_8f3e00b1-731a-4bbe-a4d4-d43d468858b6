{"FileVersion": 3, "Version": 1, "VersionName": "0.2", "FriendlyName": "Procedural Content Generation Framework (PCG) Geometry Script Interop", "Description": "Extra plugin for Procedural Content Generation Framework interacting with Geometry Scripts.", "Category": "Editor", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "https://docs.unrealengine.com/latest/en-US/procedural-content-generation--framework-in-unreal-engine/", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": true, "Installed": false, "Modules": [{"Name": "PCGGeometryScriptInterop", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "PCG", "Enabled": true}, {"Name": "GeometryScripting", "Enabled": true}]}