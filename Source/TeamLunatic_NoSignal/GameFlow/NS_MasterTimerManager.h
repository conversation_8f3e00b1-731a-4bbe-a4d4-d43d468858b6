#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/TimerHandle.h"
#include "NS_MasterTimerManager.generated.h"

// 타이머 태스크 구조체
USTRUCT(BlueprintType)
struct FTimerTask
{
    GENERATED_BODY()

    // 실행 간격 (초)
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Interval = 1.0f;

    // 마지막 실행 시간
    float LastExecuteTime = 0.0f;

    // 우선순위 (1=높음, 5=낮음)
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 Priority = 3;

    // 실행할 함수
    TFunction<void()> TaskFunction;

    // 활성화 여부
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsActive = true;

    // 태스크 이름 (디버깅용)
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString TaskName = TEXT("UnnamedTask");

    // 생성자
    FTimerTask()
    {
        Interval = 1.0f;
        LastExecuteTime = 0.0f;
        Priority = 3;
        bIsActive = true;
        TaskName = TEXT("UnnamedTask");
    }

    FTimerTask(float InInterval, int32 InPriority, TFunction<void()> InFunction, const FString& InName)
        : Interval(InInterval)
        , LastExecuteTime(0.0f)
        , Priority(InPriority)
        , TaskFunction(InFunction)
        , bIsActive(true)
        , TaskName(InName)
    {
    }
};

/**
 * 마스터 타이머 매니저 - 모든 타이머를 통합 관리
 */
UCLASS(BlueprintType, Blueprintable)
class TEAMLUNATIC_NOSIGNAL_API UNS_MasterTimerManager : public UObject
{
    GENERATED_BODY()

public:
    UNS_MasterTimerManager();

    // 마스터 타이머 시작
    UFUNCTION(BlueprintCallable, Category = "Timer")
    void StartMasterTimer();

    // 마스터 타이머 정지
    UFUNCTION(BlueprintCallable, Category = "Timer")
    void StopMasterTimer();

    // 태스크 등록
    UFUNCTION(BlueprintCallable, Category = "Timer")
    int32 RegisterTask(float Interval, int32 Priority, const FString& TaskName);

    // C++ 함수용 태스크 등록
    int32 RegisterTask(float Interval, int32 Priority, TFunction<void()> TaskFunction, const FString& TaskName);

    // 태스크 활성화/비활성화
    UFUNCTION(BlueprintCallable, Category = "Timer")
    void SetTaskActive(int32 TaskID, bool bActive);

    // 태스크 제거
    UFUNCTION(BlueprintCallable, Category = "Timer")
    void RemoveTask(int32 TaskID);

    // 모든 태스크 제거
    UFUNCTION(BlueprintCallable, Category = "Timer")
    void ClearAllTasks();

    // 태스크 간격 변경
    UFUNCTION(BlueprintCallable, Category = "Timer")
    void SetTaskInterval(int32 TaskID, float NewInterval);

    // 디버그 정보 출력
    UFUNCTION(BlueprintCallable, Category = "Timer")
    void PrintDebugInfo();

protected:
    // 마스터 타이머 핸들
    FTimerHandle MasterTimerHandle;

    // 태스크 큐
    UPROPERTY()
    TArray<FTimerTask> TaskQueue;

    // 다음 태스크 ID
    int32 NextTaskID = 0;

    // 마스터 타이머 실행 간격 (초)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timer")
    float MasterTickInterval = 1.0f;

    // 프레임당 최대 실행 태스크 수 (성능 제어)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timer")
    int32 MaxTasksPerFrame = 5;

    // 마스터 타이머 실행 함수
    void ExecuteTasks();

    // 태스크 우선순위 정렬
    void SortTasksByPriority();

    // 월드 시간 가져오기
    float GetWorldTime() const;

private:
    // 태스크 ID 맵핑
    TMap<int32, int32> TaskIDToIndex;
};
