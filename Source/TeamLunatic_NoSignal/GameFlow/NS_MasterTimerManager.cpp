#include "GameFlow/NS_MasterTimerManager.h"
#include "Engine/World.h"
#include "TimerManager.h"

UNS_MasterTimerManager::UNS_MasterTimerManager()
{
    MasterTickInterval = 1.0f;
    MaxTasksPerFrame = 5;
    NextTaskID = 0;
}

void UNS_MasterTimerManager::StartMasterTimer()
{
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("[MasterTimer] World가 유효하지 않습니다"));
        return;
    }

    // 기존 타이머가 있다면 정리
    if (MasterTimerHandle.IsValid())
    {
        World->GetTimerManager().ClearTimer(MasterTimerHandle);
    }

    // 마스터 타이머 시작
    World->GetTimerManager().SetTimer(
        MasterTimerHandle,
        this,
        &UNS_MasterTimerManager::ExecuteTasks,
        MasterTickInterval,
        true // 반복 실행
    );

    UE_LOG(LogTemp, Warning, TEXT("[MasterTimer] 마스터 타이머 시작됨 (%.1f초 간격)"), MasterTickInterval);
}

void UNS_MasterTimerManager::StopMasterTimer()
{
    UWorld* World = GetWorld();
    if (World && MasterTimerHandle.IsValid())
    {
        World->GetTimerManager().ClearTimer(MasterTimerHandle);
        UE_LOG(LogTemp, Warning, TEXT("[MasterTimer] 마스터 타이머 정지됨"));
    }
}

int32 UNS_MasterTimerManager::RegisterTask(float Interval, int32 Priority, const FString& TaskName)
{
    // 블루프린트용 - 실제 함수는 블루프린트에서 바인딩
    FTimerTask NewTask;
    NewTask.Interval = Interval;
    NewTask.Priority = Priority;
    NewTask.TaskName = TaskName;
    NewTask.bIsActive = true;
    NewTask.LastExecuteTime = GetWorldTime();

    int32 TaskID = NextTaskID++;
    TaskQueue.Add(NewTask);
    TaskIDToIndex.Add(TaskID, TaskQueue.Num() - 1);

    SortTasksByPriority();

    UE_LOG(LogTemp, Log, TEXT("[MasterTimer] 태스크 등록됨: %s (ID: %d, 간격: %.1f초, 우선순위: %d)"), 
        *TaskName, TaskID, Interval, Priority);

    return TaskID;
}

int32 UNS_MasterTimerManager::RegisterTask(float Interval, int32 Priority, TFunction<void()> TaskFunction, const FString& TaskName)
{
    FTimerTask NewTask(Interval, Priority, TaskFunction, TaskName);
    NewTask.LastExecuteTime = GetWorldTime();

    int32 TaskID = NextTaskID++;
    TaskQueue.Add(NewTask);
    TaskIDToIndex.Add(TaskID, TaskQueue.Num() - 1);

    SortTasksByPriority();

    UE_LOG(LogTemp, Log, TEXT("[MasterTimer] C++ 태스크 등록됨: %s (ID: %d, 간격: %.1f초, 우선순위: %d)"), 
        *TaskName, TaskID, Interval, Priority);

    return TaskID;
}

void UNS_MasterTimerManager::SetTaskActive(int32 TaskID, bool bActive)
{
    if (int32* IndexPtr = TaskIDToIndex.Find(TaskID))
    {
        int32 Index = *IndexPtr;
        if (TaskQueue.IsValidIndex(Index))
        {
            TaskQueue[Index].bIsActive = bActive;
            UE_LOG(LogTemp, Log, TEXT("[MasterTimer] 태스크 %s: %s"), 
                *TaskQueue[Index].TaskName, bActive ? TEXT("활성화") : TEXT("비활성화"));
        }
    }
}

void UNS_MasterTimerManager::RemoveTask(int32 TaskID)
{
    if (int32* IndexPtr = TaskIDToIndex.Find(TaskID))
    {
        int32 Index = *IndexPtr;
        if (TaskQueue.IsValidIndex(Index))
        {
            FString TaskName = TaskQueue[Index].TaskName;
            TaskQueue.RemoveAt(Index);
            TaskIDToIndex.Remove(TaskID);
            
            // 인덱스 재정렬
            TaskIDToIndex.Empty();
            for (int32 i = 0; i < TaskQueue.Num(); i++)
            {
                // TaskID를 다시 찾아서 매핑 (비효율적이지만 안전)
                for (auto& Pair : TaskIDToIndex)
                {
                    if (Pair.Value > Index)
                    {
                        Pair.Value--;
                    }
                }
            }

            UE_LOG(LogTemp, Log, TEXT("[MasterTimer] 태스크 제거됨: %s"), *TaskName);
        }
    }
}

void UNS_MasterTimerManager::ClearAllTasks()
{
    TaskQueue.Empty();
    TaskIDToIndex.Empty();
    NextTaskID = 0;
    UE_LOG(LogTemp, Warning, TEXT("[MasterTimer] 모든 태스크 제거됨"));
}

void UNS_MasterTimerManager::SetTaskInterval(int32 TaskID, float NewInterval)
{
    if (int32* IndexPtr = TaskIDToIndex.Find(TaskID))
    {
        int32 Index = *IndexPtr;
        if (TaskQueue.IsValidIndex(Index))
        {
            float OldInterval = TaskQueue[Index].Interval;
            TaskQueue[Index].Interval = NewInterval;
            UE_LOG(LogTemp, Log, TEXT("[MasterTimer] 태스크 간격 변경: %s (%.1f초 -> %.1f초)"), 
                *TaskQueue[Index].TaskName, OldInterval, NewInterval);
        }
    }
}

void UNS_MasterTimerManager::ExecuteTasks()
{
    if (TaskQueue.Num() == 0)
    {
        return;
    }

    float CurrentTime = GetWorldTime();
    int32 ExecutedTasks = 0;

    // 우선순위 순으로 태스크 실행
    for (int32 i = 0; i < TaskQueue.Num() && ExecutedTasks < MaxTasksPerFrame; i++)
    {
        FTimerTask& Task = TaskQueue[i];

        // 비활성화된 태스크는 건너뛰기
        if (!Task.bIsActive)
        {
            continue;
        }

        // 실행 시간이 되었는지 확인
        float TimeSinceLastExecution = CurrentTime - Task.LastExecuteTime;
        if (TimeSinceLastExecution >= Task.Interval)
        {
            // 태스크 실행
            if (Task.TaskFunction)
            {
                Task.TaskFunction();
                Task.LastExecuteTime = CurrentTime;
                ExecutedTasks++;

                UE_LOG(LogTemp, VeryVerbose, TEXT("[MasterTimer] 태스크 실행됨: %s"), *Task.TaskName);
            }
        }
    }

    // 실행된 태스크가 있으면 로그
    if (ExecutedTasks > 0)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("[MasterTimer] 이번 틱에서 %d개 태스크 실행됨"), ExecutedTasks);
    }
}

void UNS_MasterTimerManager::SortTasksByPriority()
{
    TaskQueue.Sort([](const FTimerTask& A, const FTimerTask& B)
    {
        return A.Priority < B.Priority; // 낮은 숫자가 높은 우선순위
    });

    // 인덱스 맵핑 재구성
    TaskIDToIndex.Empty();
    for (int32 i = 0; i < TaskQueue.Num(); i++)
    {
        // 이 부분은 TaskID를 저장하는 방식을 개선해야 함
        // 현재는 간단한 구현으로 진행
    }
}

float UNS_MasterTimerManager::GetWorldTime() const
{
    UWorld* World = GetWorld();
    return World ? World->GetTimeSeconds() : 0.0f;
}

void UNS_MasterTimerManager::PrintDebugInfo()
{
    UE_LOG(LogTemp, Warning, TEXT("[MasterTimer] === 디버그 정보 ==="));
    UE_LOG(LogTemp, Warning, TEXT("총 태스크 수: %d"), TaskQueue.Num());
    UE_LOG(LogTemp, Warning, TEXT("마스터 틱 간격: %.1f초"), MasterTickInterval);
    UE_LOG(LogTemp, Warning, TEXT("프레임당 최대 태스크: %d"), MaxTasksPerFrame);

    for (int32 i = 0; i < TaskQueue.Num(); i++)
    {
        const FTimerTask& Task = TaskQueue[i];
        UE_LOG(LogTemp, Warning, TEXT("태스크 %d: %s (간격: %.1f초, 우선순위: %d, 활성화: %s)"),
            i, *Task.TaskName, Task.Interval, Task.Priority, Task.bIsActive ? TEXT("예") : TEXT("아니오"));
    }
}
